import os
import sys
import logging
import codecs
import uuid
import ctypes

# CRITICAL FIX: Handle Unicode in Windows console logging
if sys.platform == "win32":
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    class UTF8StreamHandler(logging.StreamHandler):
        def __init__(self, stream=None):
            if stream is None:
                stream = sys.stdout
            if hasattr(stream, 'buffer'):
                stream = codecs.getwriter('utf-8')(stream.buffer, 'replace')
            super().__init__(stream)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trimsflow.log', encoding='utf-8'),
            UTF8StreamHandler()
        ]
    )
else:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trimsflow.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

logger = logging.getLogger(__name__)

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash, make_response
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
import json
import requests
import base64
from datetime import datetime, timedelta, date
import sqlite3
import gspread
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import subprocess
import shutil
from pathlib import Path
import re
from werkzeug.security import generate_password_hash, check_password_hash
import urllib3
from urllib.parse import unquote, quote
import tempfile
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# ENHANCED: Import pymediainfo for video duration detection
try:
    from pymediainfo import MediaInfo
    PYMEDIAINFO_AVAILABLE = True
    logger.info("PyMediaInfo successfully imported")
except ImportError:
    PYMEDIAINFO_AVAILABLE = False
    logger.info("PyMediaInfo not available - fallback to ffprobe")

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

app = Flask(__name__)
app.secret_key = 'archives-trimsflow-ultimate-secret-key-2025-production'

# CRITICAL: Increase request timeout for large file uploads
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB max file size
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)

# Configuration
JIRA_BASE_URL = 'https://servicedesk.isha.in'
JIRA_BASIC_AUTH = 'Basic YXJjaGl2ZXMuYm90OkIwdHRsZQ=='
GOOGLE_SHEETS_ID = '1ZbpyoNO2KSpVgmgoxwq1odN6TLZjnFdnGxAeSoj7HIM'
GOOGLE_SHEETS_TAB = 'Audio Codes'
GOOGLE_CREDENTIALS_PATH = 'credentials.json'
DRIVE_ROOT_FOLDER_ID = '1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG'  # YouTube Stems Drive
MOM_DRIVE_FOLDER_ID = '1hp_FAttg0pSrzgAYJkFGcXMvm2EAaXnb'   # Mom Stems Drive
EDITOR_PUBLIC_PATH = '/home/<USER>/public'
FFPROBE_PATH = 'ffprobe'
SERVER_ROOT_PATH = r'/mnt/vnas2'

# CRITICAL: Google Sheets Configuration for Processed Issues
TRIMS_DATABASE_SHEET_ID = '1ZbpyoNO2KSpVgmgoxwq1odN6TLZjnFdnGxAeSoj7HIM'
TRIMS_DATABASE_SHEET_NAME = 'Trims Database'

# CRITICAL: Processing Directories
WORKING_AREA_PATH = r'/data/dwara/workspace/edited/user/venugopal.reddy/transfer/Check/working area'
VIDEO_EDIT_PUBLIC_PATH = r'/data/dwara/workspace/edited/ingest/video-edit-pub/trial'
VIDEO_EDIT_PRIVATE_PATH = r'/data/dwara/workspace/edited/ingest/video-edit-priv1/trial'
WAV_INGEST_PATH = r'/data/dwara/workspace/edited/user/venugopal.reddy/transfer/Check/Audios/wav to ingest'
MP3_INGEST_PATH = r'/data/dwara/workspace/edited/user/venugopal.reddy/transfer/Check/Audios/mp3 to ingest'
MP3_PROCESSING_PATH = r'/data/dwara/workspace/edited/user/venugopal.reddy/transfer/Check/Audios/mp3 to process'

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# CRITICAL: Global processing state tracking
processing_states = {}

# CRITICAL: Thread pool for async operations
executor = ThreadPoolExecutor(max_workers=4)

# CRITICAL: FIXED Google Drive Service Setup
def get_google_drive_service_enhanced():
    """ENHANCED: Initialize Google Drive API service with proper Shared Drive support"""
    try:
        SCOPES = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.file'
        ]
        
        # Use the exact same credentials setup as your working script
        creds = Credentials.from_service_account_file(
            GOOGLE_CREDENTIALS_PATH, 
            scopes=SCOPES
        )
        
        service = build('drive', 'v3', credentials=creds)
        logger.info("✅ Google Drive service initialized successfully with Shared Drive support")
        
        return service
        
    except Exception as e:
        logger.error(f"❌ Error initializing enhanced Google Drive service: {e}")
        return None

def verify_drive_folder_access(service, folder_id):
    """Verify access to Google Drive folder before upload"""
    try:
        # Test folder access with Shared Drive support
        result = service.files().get(
            fileId=folder_id,
            fields='id,name,mimeType',
            supportsAllDrives=True,  # CRITICAL for Shared Drives
            supportsTeamDrives=True   # CRITICAL for legacy support
        ).execute()
        
        logger.info(f"✅ Successfully verified access to folder: {result.get('name')} ({folder_id})")
        return True
        
    except Exception as e:
        logger.error(f"❌ Cannot access folder {folder_id}: {e}")
        return False

# CRITICAL: Update Google Sheets with Status
def update_google_sheet_status(vp_number, column_letter, status):
    """Update specific column in Google Sheets with status"""
    try:
        scope = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
        creds = Credentials.from_service_account_file(GOOGLE_CREDENTIALS_PATH, scopes=scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(TRIMS_DATABASE_SHEET_ID).worksheet(TRIMS_DATABASE_SHEET_NAME)
        
        # Find row by VP Number (column A)
        try:
            vp_cell = sheet.find(vp_number)
            if vp_cell:
                row_num = vp_cell.row
                cell_range = f"{column_letter}{row_num}"
                sheet.update(cell_range, status)
                logger.info(f"SHEET_UPDATE: Updated {cell_range} with status: {status}")
                return True
            else:
                logger.error(f"SHEET_UPDATE: VP number {vp_number} not found in sheet")
                return False
        except Exception as e:
            logger.error(f"SHEET_UPDATE: Error finding VP number: {e}")
            return False
    except Exception as e:
        logger.error(f"SHEET_UPDATE: Error updating {column_letter} with {status}: {e}")
        return False

# CRITICAL: Google Sheets Append Function
def append_processed_issue_to_google_sheet(data):
    """Append processed issue metadata to Google Sheets"""
    try:
        logger.info(f"GOOGLE_SHEET: Appending processed issue to sheet: {data.get('vp_number', 'Unknown')}")
        scope = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
        creds = Credentials.from_service_account_file(GOOGLE_CREDENTIALS_PATH, scopes=scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(TRIMS_DATABASE_SHEET_ID).worksheet(TRIMS_DATABASE_SHEET_NAME)
        
        processed_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Column mapping as per user requirements
        row_data = [
            data.get('vp_number', ''),  # A: VP Number
            data.get('ocd_number', ''),  # B: OCD Number
            data.get('source_folder', ''),  # C: Source Video Path
            data.get('editor', ''),  # D: Editor
            data.get('google_drive_upload', ''),  # E: Google Drive Upload
            data.get('title', ''),  # F: Title
            data.get('duration', ''),  # G: Duration
            data.get('backup_type', ''),  # H: Backup Type
            data.get('video_type', ''),  # I: Type of Video
            data.get('date', ''),  # J: Date
            data.get('language', ''),  # K: Language
            data.get('access_type', ''),  # L: Access
            data.get('filename', ''),  # M: Generated File Name
            data.get('project_assignee', ''),  # N: Project Assignee
            processed_datetime,  # O: Processed Date
        ]
        
        # Add empty columns P-AJ for status tracking
        for _ in range(22):  # P to AJ = 22 columns
            row_data.append('')
        
        result = sheet.append_row(row_data)
        logger.info(f"GOOGLE_SHEET: Successfully appended issue {data.get('vp_number')} to Trims Database")
        return True
        
    except Exception as e:
        logger.error(f"GOOGLE_SHEET: Error appending to sheet: {str(e)}")
        return False

# CRITICAL: Format duration function
def formatDuration(duration_str):
    """Format duration from HH:MM:SS to XXMins-XXSecs"""
    try:
        logger.info(f"DURATION_FORMAT | Input: {duration_str}")
        parts = duration_str.split(':')
        if len(parts) != 3:
            logger.warning(f"DURATION_FORMAT | Invalid format: {duration_str}")
            return duration_str
        
        hours, minutes, seconds = int(parts[0]), int(parts[1]), int(parts[2])
        total_minutes = hours * 60 + minutes
        
        if total_minutes == 0 and seconds > 0:
            formatted = f"{seconds:02d}{'Sec' if seconds == 1 else 'Secs'}"
        elif total_minutes > 0:
            min_text = 'Min' if total_minutes == 1 else 'Mins'
            sec_text = 'Sec' if seconds == 1 else 'Secs'
            if seconds == 0:
                formatted = f"{total_minutes:02d}{min_text}"
            else:
                formatted = f"{total_minutes:02d}{min_text}-{seconds:02d}{sec_text}"
        else:
            formatted = '00Secs'
        
        logger.info(f"DURATION_FORMAT | Output: {formatted}")
        return formatted
        
    except Exception as e:
        logger.error(f"DURATION_FORMAT | ERROR | {str(e)}")
        return duration_str

# CRITICAL FIX: Fast duration detection with timeout and optimization
def get_video_duration_pymediainfo_optimized(file_path, timeout=60):
    """FIXED: Always use PyMediaInfo for all files with extended timeout for network files"""
    try:
        logger.info(f"OPTIMIZED_DURATION: Analyzing file: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f"OPTIMIZED_DURATION: File does not exist: {file_path}")
            return "00:00:00"
        
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        logger.info(f"OPTIMIZED_DURATION: File size: {file_size_mb:.1f} MB")
        
        if not PYMEDIAINFO_AVAILABLE:
            logger.error("OPTIMIZED_DURATION: PyMediaInfo not available and FFprobe failed")
            return "00:00:00"
        
        # FIXED: Always use PyMediaInfo with extended timeout for network files
        def parse_with_timeout():
            try:
                media_info = MediaInfo.parse(file_path)
                for track in media_info.tracks:
                    if track.duration is not None:
                        duration_ms = track.duration
                        total_seconds = int(float(duration_ms) / 1000)
                        hours = total_seconds // 3600
                        minutes = (total_seconds % 3600) // 60
                        seconds = total_seconds % 60
                        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                return "00:00:00"
            except Exception as e:
                logger.error(f"OPTIMIZED_DURATION: PyMediaInfo error: {e}")
                return "00:00:00"
        
        # Execute with extended timeout for network files
        future = executor.submit(parse_with_timeout)
        try:
            duration = future.result(timeout=timeout)
            logger.info(f"OPTIMIZED_DURATION: Successfully detected: {duration}")
            return duration
        except TimeoutError:
            logger.error(f"OPTIMIZED_DURATION: Timeout after {timeout}s")
            future.cancel()
            return "00:00:00"
            
    except Exception as e:
        logger.error(f"OPTIMIZED_DURATION: Critical error: {e}")
        return "00:00:00"


def get_video_duration_ffprobe_fast(file_path):
    """FIXED: Quick ffprobe duration detection with UNC path support"""
    try:
        logger.info(f"FFPROBE_FAST: Analyzing file: {file_path}")
        
        # FIXED: Handle UNC paths properly for Windows FFprobe
        processed_path = str(file_path)
        
        # Convert forward slashes to backslashes for Windows
        if os.name == 'nt':  # Windows
            processed_path = processed_path.replace('/', '\\')
            
            # For UNC paths, ensure proper formatting
            if processed_path.startswith('\\\\'):
                # UNC path - wrap in quotes for FFprobe
                processed_path = f'"{processed_path}"'
                logger.info(f"FFPROBE_FAST: Using quoted UNC path: {processed_path}")
        
        # FIXED: Optimized ffprobe command with proper path handling
        ffprobe_cmd = [
            FFPROBE_PATH,
            '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            '-probesize', '50M',  # Limit probe size for speed
            '-analyzeduration', '10M',  # Limit analysis duration
        ]
        
        # Add the file path as a separate argument (not in quotes if already quoted)
        if processed_path.startswith('"') and processed_path.endswith('"'):
            # Path is already quoted, add without shell=True
            ffprobe_cmd.append(processed_path[1:-1])  # Remove quotes for subprocess
        else:
            ffprobe_cmd.append(processed_path)
        
        logger.info(f"FFPROBE_FAST: Command: {' '.join(ffprobe_cmd[:7])} [file_path]")
        
        # FIXED: Execute with proper shell handling for Windows UNC paths
        result = subprocess.run(
            ffprobe_cmd,
            capture_output=True,
            text=True,
            timeout=30,  # Increased timeout for network files
            shell=(os.name == 'nt' and processed_path.startswith('\\\\')),  # Use shell for UNC paths on Windows
            cwd=os.getcwd()  # Ensure we have a valid working directory
        )
        
        if result.returncode == 0 and result.stdout.strip():
            duration_seconds = float(result.stdout.strip())
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            logger.info(f"FFPROBE_FAST: Successfully detected: {duration_str}")
            return duration_str
        else:
            logger.error(f"FFPROBE_FAST: Failed with return code: {result.returncode}")
            if result.stderr:
                logger.error(f"FFPROBE_FAST: Error output: {result.stderr}")
            return "00:00:00"
            
    except subprocess.TimeoutExpired:
        logger.error("FFPROBE_FAST: Timeout expired")
        return "00:00:00"
    except Exception as e:
        logger.error(f"FFPROBE_FAST: Error: {e}")
        return "00:00:00"


# CRITICAL FIX: Enhanced path normalization
def normalize_and_secure_path(base_dir, relative_path):
    """FIXED: Enhanced path normalization with proper URL decoding"""
    try:
        logger.info(f"NORMALIZING PATH: base='{base_dir}', relative='{relative_path}'")
        
        if not relative_path or relative_path in ['', 'undefined', 'null']:
            logger.info("Empty path, returning base directory")
            return Path(base_dir)
        
        # Proper URL decoding with multiple passes
        try:
            decoded_path = relative_path
            for _ in range(3):  # Maximum 3 decoding passes
                new_decoded = unquote(decoded_path)
                if new_decoded == decoded_path:
                    break
                decoded_path = new_decoded
            logger.info(f"DECODED PATH: '{decoded_path}'")
        except Exception as e:
            logger.error(f"URL decode error: {e}")
            decoded_path = relative_path
        
        # Clean path separators and normalize
        clean_path = decoded_path.replace('\\', '/').strip()
        clean_path = re.sub(r'/+', '/', clean_path)
        clean_path = clean_path.strip('/')
        
        # Handle Windows drive letters
        if len(clean_path) > 1 and clean_path[1] == ':':
            return Path(clean_path)
        
        logger.info(f"CLEANED PATH: '{clean_path}'")
        
        # Create secure paths using pathlib
        base_path = Path(base_dir).resolve()
        if clean_path:
            full_path = (base_path / clean_path).resolve()
        else:
            full_path = base_path
        
        # Security validation
        # DISABLED: Security validation completely bypassed
        logger.info(f"✅ SECURITY DISABLED - PATH ACCEPTED: '{full_path}'")
        # No security checks - accept any path

        
        return full_path
        
    except Exception as e:
        logger.error(f"PATH NORMALIZATION ERROR: {e}")
        return Path(base_dir)

def validate_and_fix_windows_path(path_obj):
    """Enhanced Windows path validation and fixes"""
    try:
        if not path_obj.exists():
            logger.warning(f"Path does not exist: {path_obj}")
            alternatives = [
                path_obj.parent,
                Path(SERVER_ROOT_PATH),
            ]
            for alt in alternatives:
                if alt.exists():
                    logger.info(f"Using alternative path: {alt}")
                    return alt
        return path_obj
    except Exception as e:
        logger.error(f"Windows path validation error: {e}")
        return Path(SERVER_ROOT_PATH)

# CRITICAL: Database setup with proper schema
def init_db():
    """Initialize database with all required tables and columns"""
    conn = sqlite3.connect('trimsflow.db')
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            full_name TEXT,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            is_active INTEGER DEFAULT 1
        )
    ''')
    
    # Issues table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS issues (
            id TEXT PRIMARY KEY,
            title TEXT,
            assignee TEXT,
            status TEXT,
            project_assignee TEXT,
            ocd_number TEXT,
            thumbnail_title TEXT,
            backup_type TEXT,
            language TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Actions table with all required columns
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS actions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue_id TEXT,
            action TEXT,
            reason TEXT,
            user_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            filename TEXT,
            drive_folder_id TEXT,
            source_folder TEXT,
            processing_details TEXT,
            language TEXT,
            google_drive_upload TEXT,
            access_type TEXT,
            final_destination TEXT
        )
    ''')
    
    # Check and add missing columns if they don't exist
    missing_columns = [
        ('language', 'TEXT'),
        ('google_drive_upload', 'TEXT'),
        ('access_type', 'TEXT'),
        ('final_destination', 'TEXT')
    ]
    
    for column_name, column_type in missing_columns:
        try:
            cursor.execute(f"SELECT {column_name} FROM actions LIMIT 1")
        except sqlite3.OperationalError:
            cursor.execute(f"ALTER TABLE actions ADD COLUMN {column_name} {column_type}")
            logger.info(f"DATABASE: Added {column_name} column to actions table")
    
    # Activity logs table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            action TEXT,
            details TEXT,
            ip_address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create default users
    cursor.execute('SELECT * FROM users WHERE username = "admin"')
    if not cursor.fetchone():
        admin_hash = generate_password_hash('admin123')
        cursor.execute('INSERT INTO users (username, password_hash, role, full_name) VALUES (?, ?, ?, ?)',
                       ('admin', admin_hash, 'admin', 'System Administrator'))
    
    cursor.execute('SELECT * FROM users WHERE username = "user"')
    if not cursor.fetchone():
        user_hash = generate_password_hash('user123')
        cursor.execute('INSERT INTO users (username, password_hash, role, full_name) VALUES (?, ?, ?, ?)',
                       ('user', user_hash, 'user', 'Regular User'))
    
    conn.commit()
    conn.close()
    logger.info("DATABASE: Successfully initialized with all tables and default users")

# User management classes
class User(UserMixin):
    def __init__(self, id, username, role='user', full_name='', email=''):
        self.id = id
        self.username = username
        self.role = role
        self.full_name = full_name
        self.email = email

@login_manager.user_loader
def load_user(user_id):
    conn = sqlite3.connect('trimsflow.db')
    cursor = conn.cursor()
    cursor.execute('SELECT username, role, full_name, email FROM users WHERE username = ? AND is_active = 1', (user_id,))
    user_data = cursor.fetchone()
    conn.close()
    
    if user_data:
        return User(user_id, user_data[0], user_data[1], user_data[2], user_data[3])
    return None

def log_activity(user_id, action, details='', ip_address=''):
    """Log user activity to database"""
    try:
        conn = sqlite3.connect('trimsflow.db')
        cursor = conn.cursor()
        cursor.execute('INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)',
                       (user_id, action, details, ip_address))
        conn.commit()
        conn.close()
    except Exception as e:
        logger.error(f"Error logging activity: {e}")
# JIRA API Functions
def get_jira_headers():
    """Get JIRA API headers"""
    return {
        'Authorization': JIRA_BASIC_AUTH,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

def extract_custom_field_value(field_value):
    """Extract value from JIRA custom field"""
    try:
        if field_value is None:
            return ''
        if isinstance(field_value, dict):
            if 'errorMessage' in field_value:
                logger.warning(f"JIRA field error: {field_value}")
                return ''
            elif 'value' in field_value:
                return str(field_value['value'])
            elif 'displayName' in field_value:
                return str(field_value['displayName'])
            elif 'name' in field_value:
                return str(field_value['name'])
            elif 'customId' in field_value:
                return str(field_value['customId'])
            else:
                for key, value in field_value.items():
                    if isinstance(value, str) and value and key not in ['id', 'self']:
                        return value
                return ''
        elif isinstance(field_value, str):
            return field_value
        elif isinstance(field_value, list) and len(field_value) > 0:
            if isinstance(field_value[0], dict):
                return extract_custom_field_value(field_value[0])
            return str(field_value[0])
        return str(field_value) if field_value else ''
    except Exception as e:
        logger.error(f"Error extracting custom field value: {e}")
        return ''

def fetch_real_jira_tickets():
    """Fetch real JIRA tickets with multiple fallback queries"""
    try:
        today_str = date.today().strftime('%Y-%m-%d')
        queries_to_try = [
            f'issuetype = "Trim Backup Issue Type" AND status = "Trim Backup Done" AND created >= "2023-02-01" AND created <= "{today_str}" ORDER BY created DESC',
            f'project = VP AND created >= "2023-02-01" AND created <= "{today_str}" ORDER BY created DESC',
            'project = VP ORDER BY created DESC',
            f'created >= "-30d" ORDER BY created DESC'
        ]

        for i, jql in enumerate(queries_to_try):
            logger.info(f"Trying JQL query {i+1}: {jql}")
            url = f"{JIRA_BASE_URL}/rest/api/2/search"
            params = {
                'jql': jql,
                'maxResults': 100,
                'fields': 'key,summary,assignee,status,created,issuetype,customfield_69703,customfield_63203,issuelinks'
            }

            try:
                response = requests.get(url, headers=get_jira_headers(), params=params, verify=False, timeout=30)
                logger.info(f"Query {i+1} response status: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    total_found = data.get('total', 0)
                    logger.info(f"Query {i+1} found {total_found} tickets")
                    if total_found > 0:
                        issues = []
                        for issue in data.get('issues', []):
                            try:
                                project_assignee = ''
                                if issue['fields'].get('customfield_69703'):
                                    field_raw = issue['fields']['customfield_69703']
                                    project_assignee = extract_custom_field_value(field_raw)

                                backup_type = ''
                                if issue['fields'].get('customfield_63203'):
                                    field_raw = issue['fields']['customfield_63203']
                                    backup_type = extract_custom_field_value(field_raw)

                                ocd_number = ''
                                ocd_key = ''
                                if issue['fields'].get('issuelinks'):
                                    for link in issue['fields']['issuelinks']:
                                        try:
                                            linked_issue = link.get('outwardIssue') or link.get('inwardIssue')
                                            if linked_issue and linked_issue.get('key', '').startswith('OCD'):
                                                ocd_key = linked_issue['key']
                                                ocd_number = linked_issue['key'].replace('OCD-', '')
                                                break
                                        except Exception as e:
                                            logger.error(f"Error processing issue link: {e}")
                                            continue

                                if not ocd_number and 'OCD' in issue['key']:
                                    ocd_key = issue['key']
                                    ocd_number = issue['key'].replace('OCD-', '')

                                issues.append({
                                    'id': issue['key'],
                                    'title': issue['fields']['summary'],
                                    'project_assignee': project_assignee,
                                    'status': issue['fields']['status']['name'],
                                    'ocd_number': ocd_number,
                                    'ocd_key': ocd_key,
                                    'backup_type': backup_type
                                })
                            except Exception as e:
                                logger.error(f"Error processing issue {issue.get('key', 'unknown')}: {e}")
                                continue

                        logger.info(f"Successfully processed {len(issues)} tickets using query {i+1}")
                        return issues
            except Exception as e:
                logger.error(f"Query {i+1} exception: {e}")
                continue

        logger.error("All JQL queries failed")
        return []
    except Exception as e:
        logger.error(f"Critical error in fetch_real_jira_tickets: {e}")
        return []

def get_thumbnail_title_from_ocd_ticket(ocd_key):
    """Get thumbnail title from OCD ticket"""
    try:
        logger.info(f"Fetching thumbnail title from OCD ticket: {ocd_key}")
        url = f"{JIRA_BASE_URL}/rest/api/2/issue/{ocd_key}"
        params = {
            'fields': 'key,summary,customfield_107520,customfield_64100'
        }

        response = requests.get(url, headers=get_jira_headers(), params=params, verify=False, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data['fields'].get('customfield_107520'):
                field_value = extract_custom_field_value(data['fields']['customfield_107520'])
                if field_value and len(field_value) > 3:
                    logger.info(f"Found Thumbnail Title from customfield_107520: {field_value}")
                    return field_value

            if data['fields'].get('customfield_64100'):
                field_value = extract_custom_field_value(data['fields']['customfield_64100'])
                if field_value and len(field_value) > 3:
                    logger.info(f"Found Video Title from customfield_64100: {field_value}")
                    return field_value

            summary = data['fields']['summary']
            logger.info(f"Using summary as fallback title: {summary}")
            return summary

        logger.warning(f"Could not fetch OCD ticket {ocd_key}: HTTP {response.status_code}")
        return f"Title for {ocd_key}"
    except Exception as e:
        logger.error(f"Error fetching thumbnail title from {ocd_key}: {e}")
        return f"Auto Title {ocd_key}"

def get_issue_details_from_jira(issue_id):
    """Get detailed issue information from JIRA"""
    try:
        logger.info(f"Fetching detailed info for issue: {issue_id}")
        url = f"{JIRA_BASE_URL}/rest/api/2/issue/{issue_id}"
        params = {
            'fields': 'key,summary,assignee,status,customfield_69703,customfield_63203,issuelinks'
        }

        response = requests.get(url, headers=get_jira_headers(), params=params, verify=False, timeout=30)
        if response.status_code == 200:
            issue = response.json()
            project_assignee = ''
            if issue['fields'].get('customfield_69703'):
                field_raw = issue['fields']['customfield_69703']
                project_assignee = extract_custom_field_value(field_raw)

            backup_type = ''
            if issue['fields'].get('customfield_63203'):
                field_raw = issue['fields']['customfield_63203']
                backup_type = extract_custom_field_value(field_raw)

            ocd_number = ''
            thumbnail_title = issue['fields']['summary']
            if issue['fields'].get('issuelinks'):
                for link in issue['fields']['issuelinks']:
                    try:
                        linked_issue = link.get('outwardIssue') or link.get('inwardIssue')
                        if linked_issue and linked_issue.get('key', '').startswith('OCD'):
                            ocd_number = linked_issue['key'].replace('OCD-', '')
                            thumbnail_title = get_thumbnail_title_from_ocd_ticket(linked_issue['key'])
                            break
                    except Exception as e:
                        logger.error(f"Error processing linked issue: {e}")
                        continue

            return {
                'editor': project_assignee,
                'ocd_number': ocd_number,
                'title': thumbnail_title,
                'backup_type': backup_type,
                'project_assignee': project_assignee
            }
        return None
    except Exception as e:
        logger.error(f"Error fetching issue details: {e}")
        return None

def get_google_sheets_client():
    """Get Google Sheets client"""
    try:
        scope = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
        creds = Credentials.from_service_account_file(GOOGLE_CREDENTIALS_PATH, scopes=scope)
        return gspread.authorize(creds)
    except Exception as e:
        logger.error(f"Google Sheets client error: {e}")
        return None

# CRITICAL: Processing Functions for Steps 1-7

def copy_folder_to_working_area(source_path, target_name):
    """BULLETPROOF copy - handles Python version compatibility + race conditions"""
    import time
    import shutil
    import stat
    import threading
    import concurrent.futures
    from pathlib import Path
    import os
    import sys
    
    try:
        source_path = source_path.replace('\\', '/')
        logger.info(f"⚡ STEP_1: BULLETPROOF copying {source_path} to Working Area")
        
        os.makedirs(WORKING_AREA_PATH, exist_ok=True)
        source = Path(source_path)
        destination = Path(WORKING_AREA_PATH) / target_name
        
        # BULLETPROOF destination cleanup
        if destination.exists():
            start_cleanup = time.time()
            try:
                os.system(f'rm -rf "{destination}"')
                time.sleep(0.3)
                logger.info(f"⚡ Cleaned destination in {time.time() - start_cleanup:.2f}s")
            except:
                pass
        
        start_time = time.time()
        
        # STAGE 1: PYTHON VERSION COMPATIBLE rsync
        logger.info("🚀 STEP_1: STAGE 1 - Python-compatible rsync...")
        
        rsync_cmd = [
            'rsync', 
            '-avhSx',
            '--progress',
            '--partial',
            '--inplace',
            '--no-whole-file',
            '--compress-level=1',
            '--skip-compress=jpg/jpeg/png/gif/zip/gz/bz2/mp4/mkv/avi/mov/wmv',
            '--modify-window=1',
            '--timeout=300',
            '--contimeout=30',
            '--bwlimit=0',
            '--delete-after',
            '--force',
            '--ignore-errors',
            '--stats',
            f"{source}/",
            str(destination)
        ]
        
        rsync_success = False
        try:
            # CRITICAL FIX: Python version compatibility
            if sys.version_info >= (3, 7):
                result = subprocess.run(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=900,
                    text=True  # Only use text=True on Python 3.7+
                )
            else:
                result = subprocess.run(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=900,
                    universal_newlines=True  # Use universal_newlines for older Python
                )
            
            stage1_time = time.time() - start_time
            
            # Parse rsync output for file count
            files_transferred = 0
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if 'Number of regular files transferred:' in line:
                        try:
                            files_transferred = int(line.split(':')[1].strip().replace(',', ''))
                        except:
                            pass
                        break
            
            if result.returncode in [0, 24]:
                logger.info(f"⚡ STAGE 1 COMPLETE: {files_transferred} files in {stage1_time:.2f}s")
                
                if destination.exists() and any(destination.iterdir()):
                    logger.info(f"✅ STEP_1: LIGHTNING copy completed in {stage1_time:.2f}s")
                    return str(destination)
            
            logger.warning(f"⚠️ STAGE 1: Partial success - proceeding to completion...")
            rsync_success = result.returncode in [0, 24]
            
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ STAGE 1: Timeout - proceeding to completion...")
        except Exception as e:
            logger.warning(f"⚠️ STAGE 1: {e} - proceeding to completion...")
        
        # STAGE 2: RACE-CONDITION-PROOF COMPLETION
        logger.info("🚀 STEP_1: STAGE 2 - Race-condition-proof completion...")
        
        def get_file_list_safe(directory):
            """Race-condition-safe file listing"""
            files = []
            try:
                for root, dirs, filenames in os.walk(directory):
                    root_path = Path(root)
                    for filename in filenames:
                        file_path = root_path / filename
                        # CRITICAL: Verify file still exists before adding to list
                        try:
                            if file_path.exists() and file_path.is_file():
                                files.append(file_path)
                        except (OSError, IOError):
                            # File disappeared - skip it
                            logger.debug(f"Skipping vanished file: {file_path}")
                            continue
            except Exception as e:
                logger.error(f"Error listing files: {e}")
            return files
        
        def parallel_copy_missing_files(max_workers=6):
            """Race-condition-safe parallel copying"""
            copied_count = 0
            failed_count = 0
            
            def copy_single_file_safe(src_file):
                nonlocal copied_count, failed_count
                try:
                    # Double-check file exists before processing
                    if not src_file.exists() or not src_file.is_file():
                        return False
                    
                    rel_path = src_file.relative_to(source)
                    dst_file = destination / rel_path
                    
                    # Check if copy needed
                    need_copy = False
                    if not dst_file.exists():
                        need_copy = True
                    else:
                        try:
                            if dst_file.stat().st_size != src_file.stat().st_size:
                                need_copy = True
                        except (OSError, IOError):
                            need_copy = True
                    
                    if need_copy:
                        try:
                            # Ensure directory exists
                            dst_file.parent.mkdir(parents=True, exist_ok=True)
                            
                            # RACE-CONDITION-SAFE copy with retry
                            max_retries = 2
                            for attempt in range(max_retries):
                                try:
                                    # Double-check source still exists
                                    if not src_file.exists():
                                        logger.debug(f"Source vanished during copy: {src_file}")
                                        return False
                                    
                                    shutil.copy2(src_file, dst_file)
                                    copied_count += 1
                                    return True
                                    
                                except (OSError, IOError) as e:
                                    if attempt < max_retries - 1:
                                        time.sleep(0.1)  # Brief pause before retry
                                        continue
                                    else:
                                        logger.debug(f"Copy failed after retries: {src_file} -> {e}")
                                        failed_count += 1
                                        return False
                        except Exception as e:
                            logger.debug(f"Copy error: {src_file} -> {e}")
                            failed_count += 1
                            return False
                    
                    return True
                    
                except Exception as e:
                    logger.debug(f"File processing error: {src_file} -> {e}")
                    failed_count += 1
                    return False
            
            # Get source files with race-condition protection
            src_files = get_file_list_safe(source)
            logger.info(f"⚡ Found {len(src_files)} source files to verify")
            
            if not src_files:
                logger.warning("No source files found - using rsync result")
                return 0
            
            # Parallel processing with error tolerance
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(copy_single_file_safe, file) for file in src_files]
                
                for i, future in enumerate(concurrent.futures.as_completed(futures)):
                    try:
                        future.result()
                        if (i + 1) % 200 == 0:
                            logger.info(f"⚡ Processed {i + 1}/{len(src_files)} files...")
                    except Exception as e:
                        logger.debug(f"Thread error: {e}")
            
            logger.info(f"⚡ Copy summary: {copied_count} copied, {failed_count} failed/vanished")
            return copied_count
        
        stage2_start = time.time()
        additional_copied = parallel_copy_missing_files()
        stage2_time = time.time() - stage2_start
        
        if additional_copied > 0:
            logger.info(f"⚡ STAGE 2: Completed {additional_copied} additional files in {stage2_time:.2f}s")
        
        total_time = time.time() - start_time
        
        # INTELLIGENT VERIFICATION: Allow for vanished files
        if destination.exists():
            try:
                dest_files = get_file_list_safe(destination)
                src_files = get_file_list_safe(source)
                
                logger.info(f"📊 VERIFICATION: Source={len(src_files)}, Destination={len(dest_files)}")
                
                # INTELLIGENT: Allow for temporary/vanished files (95% threshold)
                success_threshold = max(1, int(len(src_files) * 0.95))
                
                if len(dest_files) >= success_threshold:
                    logger.info(f"✅ STEP_1: BULLETPROOF copy SUCCESS! {len(dest_files)} files in {total_time:.2f}s")
                    logger.info(f"🚀 PERFORMANCE: {len(dest_files) / total_time:.0f} files/second")
                    return str(destination)
                else:
                    logger.error(f"❌ VERIFICATION FAILED: Only {len(dest_files)}/{len(src_files)} files copied")
                    
            except Exception as e:
                logger.error(f"Verification error: {e}")
        
        logger.error("❌ STEP_1: All methods failed")
        return None
        
    except Exception as e:
        logger.error(f"❌ STEP_1: Critical error: {e}")
        return None



def rename_folder_in_working_area(current_path, new_name):
    """Step 2: Rename folder with generated filename"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        current_path = current_path.replace('\\', '/')
        
        logger.info(f"🏷️ STEP_2: Renaming folder {current_path} to {new_name}")
        current = Path(current_path)
        parent = current.parent
        new_path = parent / new_name
        
        if new_path.exists() and new_path != current:
            shutil.rmtree(new_path)
            logger.info(f"🗑️ STEP_2: Removed existing folder: {new_path}")
        
        current.rename(new_path)
        logger.info(f"✅ STEP_2: Successfully renamed folder to {new_path}")
        return str(new_path)
    except Exception as e:
        logger.error(f"❌ STEP_2: Error renaming folder: {e}")
        return None

def normalize_output_folder(parent_folder):
    """Step 3: Normalize Output folder name (case-safe on all OS)."""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        parent_folder = parent_folder.replace('\\', '/')
    
        logger.info(f"📂 STEP_3: Normalizing Output folder in {parent_folder}")
        parent = Path(parent_folder)
        candidates = ['output', 'outputs', 'out', 'out-put', 'OutPut', 'Output', 'Out-put']
        
        for candidate in candidates:
            folder_path = parent / candidate
            if not folder_path.is_dir():
                continue

            target_path = parent / 'Output'

            # Try to detect if both paths refer to the same directory on disk
            same_on_disk = False
            if folder_path.exists() and target_path.exists():
                try:
                    same_on_disk = os.path.samefile(folder_path, target_path)
                except (FileNotFoundError, OSError):
                    same_on_disk = False

            if same_on_disk:
                # CASE-ONLY change: do a temp double-rename (safe on Windows)
                temp_name = ".normalize_tmp_" + uuid.uuid4().hex
                temp_path = parent / temp_name
                if temp_path.exists():
                    # extremely unlikely, but ensure temp doesn't exist
                    raise FileExistsError(f"Temporary path already exists: {temp_path}")
                logger.info(f"🔁 STEP_3 (case-only): Renaming {folder_path} -> {temp_path} -> {target_path}")
                folder_path.rename(temp_path)      # rename actual dir to temp
                temp_path.rename(target_path)      # rename temp to desired case
                logger.info(f"✅ STEP_3: Case-adjusted {folder_path} to {target_path}")
                return str(target_path)

            # If target exists and is a different directory, remove it first
            if target_path.exists():
                logger.info(f"🧹 STEP_3: Removing existing different target {target_path}")
                shutil.rmtree(target_path)

            # Normal rename (works cross-platform when not case-only)
            logger.info(f"🔁 STEP_3: Renaming {folder_path} -> {target_path}")
            folder_path.rename(target_path)
            logger.info(f"✅ STEP_3: Renamed {folder_path} to {target_path}")
            return str(target_path)

        logger.warning(f"⚠️ STEP_3: No Output folder found in {parent_folder}")
        return None

    except Exception as e:
        logger.error(f"❌ STEP_3: Error normalizing Output folder: {e}", exc_info=True)
        return None

def rename_video_file_to_ocd(output_folder, ocd_number):
    """Step 4: FIXED - Rename video file to OCD-Number.extension with smart detection"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        output_folder = output_folder.replace('\\', '/')
    
        logger.info(f"🎬 STEP_4: Renaming video file in {output_folder} to OCD-{ocd_number}")
        
        folder = Path(output_folder)
        video_exts = {'.mov', '.mp4', '.avi', '.mkv', '.mxf', '.wmv'}
        video_files = [f for f in folder.iterdir() if f.is_file() and f.suffix.lower() in video_exts]
        
        if not video_files:
            logger.error(f"❌ STEP_4: No video files found in {output_folder}")
            return None
        
        # Get the largest video file (main video)
        main_video = max(video_files, key=lambda x: x.stat().st_size)
        target_name = f"OCD-{ocd_number}{main_video.suffix}"
        
        # FIXED: Check if file already has correct name
        if main_video.name == target_name:
            logger.info(f"✅ STEP_4: Video file already has correct name: {target_name}")
            return str(main_video)
        
        # FIXED: Check if target file already exists
        new_path = folder / target_name
        if new_path.exists() and new_path != main_video:
            logger.info(f"🗑️ STEP_4: Removing existing target file: {target_name}")
            new_path.unlink()
        
        # FIXED: Only rename if names are different
        if main_video.name != target_name:
            try:
                main_video.rename(new_path)
                logger.info(f"✅ STEP_4: Successfully renamed {main_video.name} to {target_name}")
                return str(new_path)
            except FileNotFoundError as e:
                logger.error(f"❌ STEP_4: File not found during rename: {e}")
                # Check if source file still exists
                if main_video.exists():
                    logger.info(f"✅ STEP_4: Source file still exists, returning original path: {main_video}")
                    return str(main_video)
                else:
                    logger.error(f"❌ STEP_4: Source file disappeared: {main_video}")
                    return None
            except Exception as e:
                logger.error(f"❌ STEP_4: Unexpected error during rename: {e}")
                return str(main_video) if main_video.exists() else None
        else:
            logger.info(f"✅ STEP_4: No rename needed, file already correct: {main_video.name}")
            return str(main_video)
            
    except Exception as e:
        logger.error(f"❌ STEP_4: Error in rename_video_file_to_ocd: {e}")
        return None


def normalize_stems_folder(parent_folder):
    """Step 5: Normalize Stems folder name"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        parent_folder = parent_folder.replace('\\', '/')
    
        logger.info(f"📁 STEP_5: Normalizing Stems folder in {parent_folder}")
        parent = Path(parent_folder)
        candidates = ['stem', 'stems', 'Stems', 'STEMS']
        
        for candidate in candidates:
            folder_path = parent / candidate
            if folder_path.is_dir():
                target_path = parent / 'Stems'
                if folder_path != target_path:
                    if target_path.exists():
                        shutil.rmtree(target_path)
                    folder_path.rename(target_path)
                    logger.info(f"✅ STEP_5: Renamed {folder_path} to {target_path}")
                return str(target_path)
        
        logger.warning(f"⚠️ STEP_5: No Stems folder found in {parent_folder}")
        return None
    except Exception as e:
        logger.error(f"❌ STEP_5: Error normalizing Stems folder: {e}")
        return None

def remove_hidden_attribute(filepath):
    """Remove hidden attribute from a file (Windows only)."""
    FILE_ATTRIBUTE_HIDDEN = 0x02
    attrs = ctypes.windll.kernel32.GetFileAttributesW(str(filepath))
    if attrs != -1 and (attrs & FILE_ATTRIBUTE_HIDDEN):
        ctypes.windll.kernel32.SetFileAttributesW(str(filepath), attrs & ~FILE_ATTRIBUTE_HIDDEN)


def rename_stems_files(stems_folder):
    """Rename files in Stems folder based on mapping rules and remove hidden attribute."""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        stems_folder = stems_folder.replace('\\', '/')
    
        logger.info(f"🏷️ STEP_6: Renaming files in Stems folder {stems_folder}")
        folder = Path(stems_folder)
        if not folder.exists():
            logger.error(f"❌ STEP_6: Stems folder does not exist: {stems_folder}")
            return False

        # ENHANCED: Comprehensive mapping with case-insensitive keys and proper handling
        mapping = {
            'super': 'All Supers',
            'supers': 'All Supers',
            'all supers': 'All Supers',
            'all-supers': 'All Supers',
            'bgm': 'All Music',
            'music': 'All Music',
            'all music': 'All Music',
            'all-music': 'All Music',
            'All-Music': 'All Music',
            'All-MUSIC': 'All Music',
            'MUSIC': 'All Music',
            'vocal': 'All Dialogue',
            'voice': 'All Dialogue',
            'dialogue': 'All Dialogue',
            'dialogues': 'All Dialogue',
            'all dialogue': 'All Dialogue',
            'all-dialogue': 'All Dialogue',
            'vo': 'All Dialogue',
            'VO': 'All Dialogue',
            'Main Voice': 'All Dialogue',
            'Main-Voice': 'All Dialogue',
            'ALL DIALOGUE': 'All Dialogue',
            'All-Dialogue': 'All Dialogue',
            'effects': 'All SFX',
            'sfx': 'All SFX',
            'all sfx': 'All SFX',
            'all-sfx': 'All SFX',
            'subtitles': 'All Subtitles',
            'subs': 'All Subtitles',
            'titles': 'All Subtitles',
            'title': 'All Subtitles',
            'captions': 'All Subtitles',
            'all titles': 'All Subtitles',
            'all-titles': 'All Subtitles',
            'Subtitle': 'All Subtitles',
            'All Subtitle': 'All Subtitles',
            'All Subtitles': 'All Subtitles',
            'video': 'All Video',
            'video stems': 'All Video',
            'stems-video': 'All Video',
            'all video': 'All Video',
            'all-video': 'All Video',
            'all videos': 'All Video',
            'main video': 'All Video',
            'broll': 'All Video 2',
            'b-roll': 'All Video 2',
            'all-video 2': 'All Video 2',
            'all video 2': 'All Video 2',
            'all b-rolls 2': 'All Video 2',
            'brolls': 'All Video 2',
            'All Brolls': 'All Video 2',
            'All A rolls': 'All Video',
            'All B rolls': 'All Video 2',
            'A rolls': 'All Video 1',
            'A Video 1': 'All Video 1',
            'Video1': 'All Video 1',
            'Video2': 'All Video 2',
            'A-rolls': 'All Video 1',
            'text': 'All Subtitles',
            'mix': 'All Mix',
            'MIX': 'All Mix',
            'All MIX': 'All Mix',
            'All-MIX': 'All Mix',
            'Tamil Dialouge': 'Tamil All Dialogue',
            'tamil dialogue': 'Tamil All Dialogue'
        }

        renamed_files = 0
        processed_files = set()  # Track processed files to avoid duplicates

        for file_path in folder.iterdir():
            if file_path.is_file() and file_path not in processed_files:
                file_name_lower = file_path.stem.lower()
                file_ext = file_path.suffix
                
                # Check if file is already correctly named
                is_already_correct = False
                for target_name in mapping.values():
                    if file_path.stem == target_name:
                        is_already_correct = True
                        logger.info(f"✅ STEP_6: File already correctly named: {file_path.name}")
                        break
                
                if is_already_correct:
                    processed_files.add(file_path)
                    continue

                # Find matching key for renaming
                found_match = False
                for key, new_name in mapping.items():
                    # ENHANCED: Perfect case-insensitive matching
                    key_lower = key.lower()
                    
                    # Check if the key matches anywhere in the filename (case-insensitive)
                    if key_lower in file_name_lower:
                        new_file_path = folder / f"{new_name}{file_ext}"
                        
                        logger.info(f"🔄 STEP_6: Renaming '{file_path.name}' to '{new_name}{file_ext}' (matched key: '{key}')")
                        
                        # CASE-ONLY RENAME FIX for Windows
                        if file_path.resolve() == new_file_path.resolve():
                            temp_path = folder / (f".tmp_{uuid.uuid4().hex}{file_ext}")
                            file_path.rename(temp_path)
                            try:
                                remove_hidden_attribute(temp_path)
                            except:
                                pass  # Continue even if hidden attribute removal fails
                            file_path = temp_path

                        # Remove existing target file if it exists
                        if new_file_path.exists() and new_file_path != file_path:
                            logger.info(f"🗑️ STEP_6: Removing existing target file: {new_file_path.name}")
                            new_file_path.unlink()

                        # Perform the rename
                        try:
                            file_path.rename(new_file_path)
                            try:
                                remove_hidden_attribute(new_file_path)
                            except:
                                pass  # Continue even if hidden attribute removal fails
                            logger.info(f"✅ STEP_6: Successfully renamed to {new_name}{file_ext}")
                            renamed_files += 1
                            processed_files.add(new_file_path)
                            found_match = True
                            break
                        except Exception as rename_error:
                            logger.error(f"❌ STEP_6: Failed to rename {file_path.name}: {rename_error}")
                            continue

                if not found_match:
                    processed_files.add(file_path)

                # Special case for .docx files (Script renaming)
                if file_ext.lower() == '.docx' and not found_match:
                    script_path = folder / 'Script.docx'
                    if file_path.name.lower() != 'script.docx':
                        if script_path.exists() and script_path != file_path:
                            script_path.unlink()
                        try:
                            file_path.rename(script_path)
                            try:
                                remove_hidden_attribute(script_path)
                            except:
                                pass
                            logger.info(f"📄 STEP_6: Renamed {file_path.name} to Script.docx")
                            renamed_files += 1
                        except Exception as docx_error:
                            logger.error(f"❌ STEP_6: Failed to rename docx file: {docx_error}")

        logger.info(f"✅ STEP_6: Successfully processed {len(processed_files)} files, renamed {renamed_files} files in Stems folder")
        return True

    except Exception as e:
        logger.error(f"❌ STEP_6: Error renaming Stems files: {e}")
        return False


def normalize_ilp_glp_folders(parent_folder, ocd_number):
    """Step 7: Normalize ILP/GLP/MP4 folders - FIXED to look inside Stems folder"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        parent_folder = parent_folder.replace('\\', '/')
    
        logger.info(f"📁 STEP_7: Normalizing ILP/GLP folders in {parent_folder}")
        parent = Path(parent_folder)

        # FIXED: Look for Stems folder first
        stems_folder = parent / 'Stems'
        if not stems_folder.exists() or not stems_folder.is_dir():
            logger.warning(f"⚠️ STEP_7: No Stems folder found in {parent_folder}")
            return None

        logger.info(f"📁 STEP_7: Found Stems folder, searching inside: {stems_folder}")

        candidates = ['ILP', 'GLP', 'ILP & Glp', 'ILP & GLP', 'MP4', 'mp4', 'Mp4', 'ILP and GLP. Mp4', 'ILP Stems', 'ilp and glp']
        target_name = 'ILP GLP MP4'

        # FIXED: Search inside Stems folder instead of parent folder
        for candidate in candidates:
            folder_path = stems_folder / candidate
            if folder_path.is_dir():
                target_path = stems_folder / target_name
                if folder_path != target_path:
                    if target_path.exists():
                        shutil.rmtree(target_path)
                    folder_path.rename(target_path)
                    logger.info(f"✅ STEP_7: Renamed {folder_path} to {target_path}")

                # Rename files inside the renamed folder
                rename_ilp_glp_files(str(target_path), ocd_number)
                return str(target_path)

        logger.warning(f"⚠️ STEP_7: No ILP/GLP folders found inside Stems folder: {stems_folder}")
        return None
    except Exception as e:
        logger.error(f"❌ STEP_7: Error normalizing ILP/GLP folders: {e}")
        return None

def rename_ilp_glp_files(ilp_glp_folder, ocd_number):
    """Step 7b: ENHANCED - Rename files inside ILP GLP MP4 folder based on filename patterns"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        ilp_glp_folder = ilp_glp_folder.replace('\\\\', '/')
        
        logger.info(f"🏷️ STEP_7b: Renaming files in {ilp_glp_folder}")
        folder = Path(ilp_glp_folder)
        files = [f for f in folder.iterdir() if f.is_file()]
        
        if not files:
            logger.warning(f"⚠️ STEP_7b: No files found in {ilp_glp_folder}")
            return False
            
        renamed_count = 0
        processed_files = set()  # Track processed files to avoid duplicates
        
        # ENHANCED: Define comprehensive pattern matching - ANYWHERE in filename
        ocd_patterns = ['output', 'final', 'ocd', 'a1']  # These can appear ANYWHERE in filename
        video_patterns = ['video', 'all video', 'stems', 'a2']  # These can appear ANYWHERE in filename
        
        for file_path in files:
            if file_path in processed_files:
                continue
                
            # Skip non-video files
            video_extensions = {'.mp4', '.mov', '.avi', '.mkv', '.mxf', '.wmv'}
            if file_path.suffix.lower() not in video_extensions:
                logger.info(f"⏭️ STEP_7b: Skipping non-video file: {file_path.name}")
                processed_files.add(file_path)
                continue
                
            # PERFECT: Case-insensitive pattern detection ANYWHERE in filename
            filename_lower = file_path.name.lower()  # Convert entire filename to lowercase
            target_name = None
            rename_reason = ""
            matched_pattern = ""
            
            # Check for OCD patterns ANYWHERE in filename (output, final, OCD, a1)
            ocd_match_found = False
            for pattern in ocd_patterns:
                # Check if pattern exists ANYWHERE in the lowercase filename
                if pattern.lower() in filename_lower:
                    target_name = f"OCD-{ocd_number}{file_path.suffix}"
                    rename_reason = f"found '{pattern}' anywhere in filename"
                    matched_pattern = pattern
                    ocd_match_found = True
                    logger.info(f"🎯 STEP_7b: Found OCD pattern '{pattern}' in filename: {file_path.name}")
                    break
            
            # Check for Video patterns ANYWHERE in filename (video, all video, stems, A2)
            if not ocd_match_found:
                for pattern in video_patterns:
                    # Check if pattern exists ANYWHERE in the lowercase filename
                    if pattern.lower() in filename_lower:
                        target_name = f"All Video{file_path.suffix}"
                        rename_reason = f"found '{pattern}' anywhere in filename"
                        matched_pattern = pattern
                        logger.info(f"🎯 STEP_7b: Found Video pattern '{pattern}' in filename: {file_path.name}")
                        break
            
            # If no pattern matches anywhere in filename, skip this file
            if not target_name:
                logger.info(f"⏭️ STEP_7b: No matching pattern found anywhere in filename: {file_path.name}")
                processed_files.add(file_path)
                continue
                
            target_path = folder / target_name
            
            # Check if file already has correct name
            if file_path.name == target_name:
                logger.info(f"✅ STEP_7b: File already correctly named: {file_path.name}")
                processed_files.add(file_path)
                continue
                
            # Handle existing target files
            existing_target = None
            for existing_file in folder.iterdir():
                if existing_file.is_file() and existing_file.name.lower() == target_name.lower() and existing_file != file_path:
                    existing_target = existing_file
                    break
                    
            if existing_target:
                logger.info(f"🗑️ STEP_7b: Removing existing target: {existing_target.name}")
                try:
                    existing_target.unlink()
                except Exception as remove_error:
                    logger.error(f"❌ STEP_7b: Failed to remove existing target {existing_target.name}: {remove_error}")
                    processed_files.add(file_path)
                    continue
            
            # Handle case-only renames (Windows compatibility)
            if file_path.resolve() == target_path.resolve():
                temp_name = f".tmp_{uuid.uuid4().hex}{file_path.suffix}"
                temp_path = folder / temp_name
                try:
                    logger.info(f"🔄 STEP_7b: Case-only rename via temp: {file_path.name} → {target_name}")
                    file_path.rename(temp_path)
                    temp_path.rename(target_path)
                    logger.info(f"✅ STEP_7b: Case-only rename completed (found '{matched_pattern}' in filename)")
                    renamed_count += 1
                    processed_files.add(target_path)
                    continue
                except Exception as case_error:
                    logger.error(f"❌ STEP_7b: Case-only rename failed {file_path.name}: {case_error}")
                    if temp_path.exists():
                        try:
                            temp_path.rename(file_path)
                        except:
                            pass
                    processed_files.add(file_path)
                    continue
            
            # Standard rename operation
            try:
                logger.info(f"🔄 STEP_7b: Renaming '{file_path.name}' → '{target_name}' (matched: '{matched_pattern}')")
                file_path.rename(target_path)
                logger.info(f"✅ STEP_7b: Successfully renamed - {rename_reason}")
                renamed_count += 1
                processed_files.add(target_path)
            except Exception as rename_error:
                logger.error(f"❌ STEP_7b: Rename failed for {file_path.name}: {rename_error}")
                processed_files.add(file_path)
        
        # Summary logging
        total_video_files = len([f for f in folder.iterdir() if f.is_file() and f.suffix.lower() in video_extensions])
        logger.info(f"✅ STEP_7b: Processed {total_video_files} video files, renamed {renamed_count} files")
        logger.info(f"🔍 STEP_7b: OCD patterns searched: {ocd_patterns}")
        logger.info(f"🔍 STEP_7b: Video patterns searched: {video_patterns}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ STEP_7b: Critical error in pattern-based renaming: {e}")
        return False


def get_video_duration_from_folder_optimized(source_folder):
    """ENHANCED: Find video in Output folder and get duration using optimized detection"""
    try:
        logger.info(f"🎥 ENHANCED: Detecting video duration from folder: {source_folder}")
        # FIXED: Proper path handling for duration detection
        if source_folder.startswith(SERVER_ROOT_PATH) or source_folder.startswith(SERVER_ROOT_PATH.replace('\\', '/')):
            # Path is already absolute from server root - use as is
            normalized_path = Path(source_folder)
            logger.info(f"📂 USING ABSOLUTE PATH: {normalized_path}")
        else:
            # Path is relative - combine with server root
            normalized_path = Path(SERVER_ROOT_PATH) / source_folder
            logger.info(f"📂 COMBINING PATHS: {SERVER_ROOT_PATH} + {source_folder} = {normalized_path}")
        
        validated_path = validate_and_fix_windows_path(normalized_path)
        logger.info(f"📂 NORMALIZED FOLDER PATH: {validated_path}")
        
        if not validated_path.exists():
            logger.warning(f"⚠️ Validated path does not exist: {validated_path}")
            return "00Min-00Secs"
        
        # Look for Output folder
        output_folder = None
        try:
            for item in validated_path.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    logger.info(f"🔍 Checking folder: {item.name}")
                    if folder_name_lower in ['output', 'outputs', 'out', 'final', 'export', 'rendered']:
                        output_folder = item
                        logger.info(f"✅ Found output folder: {output_folder}")
                        break
        except Exception as e:
            logger.error(f"❌ Error scanning directory: {e}")
            return "00Min-00Secs"
        
        if not output_folder:
            logger.warning(f"⚠️ No Output folder found in: {validated_path}")
            return "00Min-00Secs"
        
        # Search for video files
        video_files = []
        video_extensions = ['*.mov', '*.MOV', '*.mp4', '*.MP4', '*.avi', '*.AVI', '*.mkv', '*.MKV', '*.mxf', '*.MXF']
        
        for ext in video_extensions:
            try:
                found_files = list(output_folder.glob(ext))
                video_files.extend(found_files)
                logger.info(f"🎬 Found {len(found_files)} {ext} files")
            except Exception as e:
                logger.error(f"❌ Error searching for {ext}: {e}")
        
        if not video_files:
            logger.warning(f"⚠️ No video files found in Output folder: {output_folder}")
            return "00Min-00Secs"
        
        # Select largest video file
        try:
            main_video = max(video_files, key=lambda x: x.stat().st_size)
            file_size_mb = main_video.stat().st_size / (1024 * 1024)
            logger.info(f"🎯 Using largest video file: {main_video} ({file_size_mb:.1f} MB)")
        except Exception as e:
            logger.error(f"❌ Error selecting video file: {e}")
            return "00Min-00Secs"
        
        # Use optimized duration detection
        raw_duration = get_video_duration_pymediainfo_optimized(str(main_video))
        if raw_duration and raw_duration != "00:00:00":
            formatted_duration = formatDuration(raw_duration)
            logger.info(f"✅ ENHANCED: Final duration result: {formatted_duration}")
            return formatted_duration
        else:
            return "00Min-00Secs"
            
    except Exception as e:
        logger.error(f"❌ ENHANCED: Critical error in folder duration detection: {e}")
        return "00Min-00Secs"

# FIXED: Enhanced server directory browsing function
def browse_server_directory_fixed(path=''):
    """FIXED: Enhanced server directory browsing with proper path handling"""
    try:
        logger.info(f"🔍 BROWSE: Browsing server directory with path: '{path}'")
        
        # Normalize the path
        if not path or path in ['', 'undefined', 'null']:
            validated_path = Path(SERVER_ROOT_PATH)
            logger.info(f"📂 Using root path: {validated_path}")
        else:
            # Handle relative path from server root
            decoded_path = unquote(path).replace('\\', '/').strip('/')
            full_path = Path(SERVER_ROOT_PATH) / decoded_path
            validated_path = full_path.resolve()
            
            # DISABLED: Security check completely bypassed
            logger.info(f"✅ SECURITY DISABLED - Using path as-is: {validated_path}")
            # No security validation - use any path

        
        logger.info(f"📍 FINAL PATH: {validated_path}")
        
        if not validated_path.exists():
            logger.error(f"❌ Path does not exist: {validated_path}")
            validated_path = Path(SERVER_ROOT_PATH)
        
        folders = []
        files = []
        
        # Add parent directory option (if not at root)
        try:
            if str(validated_path) != str(Path(SERVER_ROOT_PATH)):
                parent_path = validated_path.parent
                if parent_path >= Path(SERVER_ROOT_PATH):
                    relative_parent = parent_path.relative_to(Path(SERVER_ROOT_PATH))
                    folders.append({
                        'name': '..',
                        'path': str(relative_parent).replace('\\', '/'),
                        'type': 'parent',
                        'icon': 'fa-arrow-up',
                        'size': 0
                    })
        except Exception as e:
            logger.error(f"❌ Error adding parent directory: {e}")
        
        # List directories and files
        # FIXED: List directories and files with enhanced error handling
        try:
            items = list(validated_path.iterdir())
            logger.info(f"📊 Found {len(items)} items in directory")
    
            for item in items:
                # Skip hidden files but log them
                if item.name.startswith('.'):
                    logger.info(f"⏭️ Skipping hidden item: {item.name}")
                    continue
        
                try:
                    # FIXED: More robust relative path calculation
                    # FIXED: Proper path calculation for nested navigation
                    try:
                        # Calculate the correct relative path from server root
                        item_relative_to_root = item.relative_to(Path(SERVER_ROOT_PATH))
                        relative_path_str = str(item_relative_to_root).replace('\\', '/')
    
                        # Log for debugging
                        logger.info(f"🔍 ITEM: {item.name} -> PATH: {relative_path_str}")
    
                    except ValueError:
                        # Fallback: if we can't make it relative to server root, 
                        # construct path based on current path + item name
                        if path and path not in ['', 'undefined', 'null']:
                            # We're in a subdirectory, so append to current path
                            relative_path_str = f"{path.rstrip('/')}/{item.name}"
                        else:
                            # We're at root, just use item name
                            relative_path_str = item.name
    
                        logger.info(f"🔍 FALLBACK ITEM: {item.name} -> PATH: {relative_path_str}")

            
                    if item.is_dir():
                        # FIXED: Always add directories without filtering
                        folder_type = 'folder'
                        icon = 'fa-folder'
                        folder_name_lower = item.name.lower()
                
                        # Special folder icons
                        if any(special in folder_name_lower for special in ['output', 'stems', 'final', 'export']):
                            folder_type = 'special'
                            icon = 'fa-folder-open'
                        elif any(special in folder_name_lower for special in ['video', 'movie', 'film']):
                            folder_type = 'video'
                            icon = 'fa-video'
                        elif any(special in folder_name_lower for special in ['audio', 'sound', 'music']):
                            folder_type = 'audio'
                            icon = 'fa-music'
                
                        # FIXED: Safe item counting
                        try:
                            item_count = len(list(item.iterdir()))
                        except (PermissionError, OSError):
                            item_count = 0
                
                        folders.append({
                            'name': item.name,
                            'path': relative_path_str,
                            'type': folder_type,
                            'icon': icon,
                            'size': item_count
                        })
                
                        logger.info(f"✅ Added folder: {item.name}")
                
                    elif item.is_file():
                        # FIXED: Add all files, not just specific types
                        file_extension = item.suffix.lower()
                
                        # Determine file icon
                        if file_extension in ['.mov', '.mp4', '.avi', '.mkv', '.wmv', '.mts']:
                            icon = 'fa-file-video'
                        elif file_extension in ['.wav', '.mp3', '.aac', '.flac']:
                            icon = 'fa-file-audio'
                        elif file_extension in ['.srt', '.txt', '.log']:
                            icon = 'fa-file-text'
                        elif file_extension in ['.docx', '.doc', '.pdf']:
                            icon = 'fa-file-word'
                        else:
                            icon = 'fa-file'
                
                        # FIXED: Safe file size calculation
                        try:
                            file_size = item.stat().st_size
                        except (OSError, PermissionError):
                            file_size = 0
                
                        files.append({
                            'name': item.name,
                            'path': relative_path_str,
                            'type': 'file',
                            'icon': icon,
                            'size': file_size
                        })
                
                        logger.info(f"✅ Added file: {item.name}")
                
                except Exception as e:
                    logger.error(f"❌ Error processing item {item.name}: {e}")
                    # FIXED: Add error items to list instead of skipping them
                    folders.append({
                        'name': f"{item.name} (Error)",
                        'path': '',
                        'type': 'error',
                        'icon': 'fa-exclamation-triangle',
                        'size': 0
                    })
                    continue

        except PermissionError as e:
            logger.error(f"🚫 Permission denied accessing directory: {e}")
            folders.append({
                'name': 'Permission Denied - Check folder permissions',
                'path': '',
                'type': 'error',
                'icon': 'fa-lock',
                'size': 0
            })
    
        except Exception as e:
            logger.error(f"❌ Error listing directory contents: {e}")
            folders.append({
                'name': f'Error loading directory: {str(e)}',
                'path': '',
                'type': 'error', 
                'icon': 'fa-exclamation-triangle',
                'size': 0
            })
        # DEBUG: Log directory contents
        logger.info(f"📍 FINAL PATH: {validated_path}")
        logger.info(f"📍 PATH EXISTS: {validated_path.exists()}")
        logger.info(f"📍 PATH IS DIR: {validated_path.is_dir()}")

        if validated_path.exists() and validated_path.is_dir():
            try:
                debug_items = list(validated_path.iterdir())
                logger.info(f"🔍 DEBUG: Found {len(debug_items)} items in directory")
                for debug_item in debug_items:
                    logger.info(f"🔍 DEBUG ITEM: {debug_item.name} (dir: {debug_item.is_dir()}, file: {debug_item.is_file()})")
            except Exception as debug_e:
                logger.error(f"🔍 DEBUG ERROR: {debug_e}")

        
        # Prepare result
        try:
            current_path_web = str(validated_path.relative_to(Path(SERVER_ROOT_PATH))).replace('\\', '/')
        except ValueError:
            current_path_web = ''
        
        result = {
            'current_path': current_path_web,
            'absolute_path': str(validated_path),
            'folders': folders,
            'files': files[:20]  # Limit files for performance
        }
        
        logger.info(f"✅ BROWSE: Returning {len(folders)} folders and {len(files)} files")
        return result
        
    except Exception as e:
        logger.error(f"❌ BROWSE: Critical error browsing directory: {e}")
        return {
            'current_path': '',
            'absolute_path': SERVER_ROOT_PATH,
            'folders': [{
                'name': f'Error loading folders: {str(e)}',
                'path': '',
                'type': 'error',
                'icon': 'fa-exclamation-triangle',
                'size': 0
            }],
            'files': [],
            'error': str(e)
        }
# CRITICAL: Processing Functions for Steps 8-14

def copy_srt_file_to_stems(parent_folder, stems_folder, ocd_number):
    """Step 8: Copy SRT file to Stems folder"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        parent_folder = parent_folder.replace('\\', '/')
        stems_folder = stems_folder.replace('\\', '/')
    
        logger.info(f"📄 STEP_8: Copying SRT file to Stems folder")
        parent = Path(parent_folder)
        stems = Path(stems_folder)
        
        # Look for SRT folder
        srt_candidates = ['SRT', 'srt', 'Srt']
        srt_file_found = False
        
        for candidate in srt_candidates:
            srt_folder = parent / candidate
            if srt_folder.is_dir():
                # Find .srt files
                srt_files = list(srt_folder.glob('*.srt'))
                if srt_files:
                    # Copy the first .srt file found
                    source_srt = srt_files[0]
                    target_srt = stems / f"OCD-{ocd_number}.srt"
                    
                    if target_srt.exists():
                        target_srt.unlink()
                    
                    shutil.copy2(source_srt, target_srt)
                    logger.info(f"✅ STEP_8: Copied {source_srt.name} to {target_srt.name}")
                    srt_file_found = True
                    break
        
        if not srt_file_found:
            logger.warning("⚠️ STEP_8: No SRT file found to copy")
        
        return srt_file_found
        
    except Exception as e:
        logger.error(f"❌ STEP_8: Error copying SRT file: {e}")
        return False

def validate_copied_folder(source_folder, copied_folder):
    """Step 9: Validate copied folder matches source"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        source_folder = source_folder.replace('\\', '/')
        copied_folder = copied_folder.replace('\\', '/')
    
        logger.info(f"🔍 STEP_9: Validating copied folder {copied_folder}")
        source = Path(source_folder)
        copied = Path(copied_folder)
        
        if not source.exists() or not copied.exists():
            logger.error("❌ STEP_9: Source or copied folder does not exist")
            return False
        
        # Compare folder sizes (basic validation)
        def get_folder_size(folder_path):
            total = 0
            for path in folder_path.rglob('*'):
                if path.is_file():
                    try:
                        total += path.stat().st_size
                    except (OSError, FileNotFoundError):
                        continue
            return total
        
        source_size = get_folder_size(source)
        copied_size = get_folder_size(copied)
        
        size_difference = abs(source_size - copied_size) / source_size if source_size > 0 else 0
        
        if size_difference < 0.01:  # Less than 1% difference
            logger.info(f"✅ STEP_9: Validation successful - sizes match (difference: {size_difference:.2%})")
            return True
        else:
            logger.error(f"❌ STEP_9: Validation failed - size difference: {size_difference:.2%}")
            return False
            
    except Exception as e:
        logger.error(f"❌ STEP_9: Error validating copied folder: {e}")
        return False

def extract_audio_files(output_folder, filename):
    """Step 10: FIXED - Extract audio files from main video with improved detection"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        output_folder = output_folder.replace('\\', '/')
    
        logger.info(f"🎵 STEP_10: Extracting audio files from video in {output_folder}")
        
        # Find video files in the output folder
        folder = Path(output_folder)
        video_exts = {'.mov', '.mp4', '.avi', '.mkv', '.mxf'}
        video_files = [f for f in folder.iterdir() if f.is_file() and f.suffix.lower() in video_exts]
        
        if not video_files:
            logger.error("❌ STEP_10: No video file found for audio extraction")
            return False
        
        # FIXED: Get the largest video file (should be the main video)
        main_video = max(video_files, key=lambda x: x.stat().st_size)
        logger.info(f"🎯 STEP_10: Using video file: {main_video.name}")
        
        # Create output directories
        os.makedirs(WAV_INGEST_PATH, exist_ok=True)
        os.makedirs(MP3_INGEST_PATH, exist_ok=True)
        os.makedirs(MP3_PROCESSING_PATH, exist_ok=True)
        
        # Extract audio files
        wav_output = Path(WAV_INGEST_PATH) / f"{filename}.wav"
        mp3_output1 = Path(MP3_INGEST_PATH) / f"{filename}.mp3"
        mp3_output2 = Path(MP3_PROCESSING_PATH) / f"{filename}.mp3"
        
        # Extract WAV
        wav_cmd = [
            'ffmpeg', '-i', str(main_video), '-vn', '-acodec', 'pcm_s16le', '-ar', '44100', '-ac', '2',
            str(wav_output), '-y'
        ]
        
        # Extract MP3 (first copy)
        mp3_cmd1 = [
            'ffmpeg', '-i', str(main_video), '-vn', '-acodec', 'mp3', '-ab', '192k',
            str(mp3_output1), '-y'
        ]
        
        # Extract MP3 (second copy)
        mp3_cmd2 = [
            'ffmpeg', '-i', str(main_video), '-vn', '-acodec', 'mp3', '-ab', '192k',
            str(mp3_output2), '-y'
        ]
        
        # Execute commands with better error handling
        try:
            subprocess.run(wav_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=300)
            logger.info(f"✅ STEP_10: Extracted WAV to {wav_output}")
            
            subprocess.run(mp3_cmd1, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=300)
            logger.info(f"✅ STEP_10: Extracted MP3 to {mp3_output1}")
            
            subprocess.run(mp3_cmd2, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=300)
            logger.info(f"✅ STEP_10: Extracted MP3 to {mp3_output2}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ STEP_10: FFmpeg error: {e}")
            if e.stderr:
                logger.error(f"❌ STEP_10: FFmpeg stderr: {e.stderr}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("⏱️ STEP_10: FFmpeg timeout")
            return False
            
    except Exception as e:
        logger.error(f"❌ STEP_10: Error extracting audio files: {e}")
        return False


# FIXED: Upload to Google Drive Function with proper Shared Drive support
def upload_to_google_drive(local_folder, ocd_number, title, upload_to_drive, issue_id, video_type):
    """Step 11: ENHANCED - Upload folder to Google Drive with video-type based folder selection"""
    try:
        if upload_to_drive.lower() != 'yes':
            logger.info("⏭️ STEP_11: Skipping Google Drive upload as per user preference")
            # Update Google Sheet with skipped status
            update_google_sheet_status(issue_id, 'AE', 'No')  # Google Drive Upload
            update_google_sheet_status(issue_id, 'AF', 'N/A')  # Stems Folder Name
            update_google_sheet_status(issue_id, 'AG', 'N/A')  # Stems Drive Link
            return "Skipped", "Not uploaded"
        
        # ENHANCED: Select drive folder based on video type
        if video_type == "Miracle-Of-Mind":
            selected_drive_folder_id = MOM_DRIVE_FOLDER_ID
            drive_name = "Mom Stems Drive"
            logger.info(f"🧘 STEP_11: Selected Mom Stems Drive for Miracle-Of-Mind video")
        else:
            selected_drive_folder_id = DRIVE_ROOT_FOLDER_ID
            drive_name = "YouTube Stems Drive"
            logger.info(f"🎬 STEP_11: Selected YouTube Stems Drive for {video_type} video")
        
        logger.info(f"☁️ STEP_11: Uploading folder to {drive_name} with Shared Drive support")
        
        if not os.path.exists(local_folder):
            error_msg = f"Local folder does not exist: {local_folder}"
            logger.error(f"❌ STEP_11: {error_msg}")
            # Update Google Sheet with failure status
            update_google_sheet_status(issue_id, 'AE', 'No')
            update_google_sheet_status(issue_id, 'AF', 'Failed')
            update_google_sheet_status(issue_id, 'AG', 'Failed')
            return None, None
        
        # FIXED: Enhanced Google Drive service initialization
        service = get_google_drive_service_enhanced()
        if not service:
            error_msg = "Could not initialize Google Drive service"
            logger.error(f"❌ STEP_11: {error_msg}")
            # Update Google Sheet with failure status
            update_google_sheet_status(issue_id, 'AE', 'No')
            update_google_sheet_status(issue_id, 'AF', 'Failed')
            update_google_sheet_status(issue_id, 'AG', 'Failed')
            return None, None
        
        # ENHANCED: Verify folder access with selected drive folder
        if not verify_drive_folder_access(service, selected_drive_folder_id):
            error_msg = f"Cannot access {drive_name} folder: {selected_drive_folder_id}"
            logger.error(f"❌ STEP_11: {error_msg}")
            # Update Google Sheet with failure status
            update_google_sheet_status(issue_id, 'AE', 'No')
            update_google_sheet_status(issue_id, 'AF', 'Failed')
            update_google_sheet_status(issue_id, 'AG', 'Failed')
            return None, None
        
        # Create main folder name: OCD-<number>-<title> (replace spaces with hyphens)
        safe_title = title.replace(' ', '-').replace('/', '-').replace('\\', '-')
        folder_name = f"OCD-{ocd_number}-{safe_title}"
        
        # ENHANCED: Create folder in selected Google Drive with Shared Drive support
        folder_metadata = {
            'name': folder_name,
            'mimeType': 'application/vnd.google-apps.folder',
            'parents': [selected_drive_folder_id]  # Use selected folder ID
        }
        
        folder = service.files().create(
            body=folder_metadata, 
            fields='id',
            supportsAllDrives=True,  # CRITICAL: Support for Shared Drives
            supportsTeamDrives=True   # CRITICAL: Legacy support
        ).execute()
        
        folder_id = folder.get('id')
        logger.info(f"📁 STEP_11: Created folder in {drive_name}: {folder_name} (ID: {folder_id})")
        
        # [REST OF YOUR EXISTING UPLOAD LOGIC REMAINS THE SAME]
        # FIXED: Upload folder contents with Shared Drive support and ALL files in Stems
        def upload_folder_recursive(local_path, parent_id, is_stems_folder=False):
            local_path = Path(local_path)
            uploaded_count = 0
            for item in local_path.iterdir():
                if item.is_file():
                    # Skip .DS_Store, .mov files, and large files > 1GB for all files inside Stems folder
                    if item.name.lower() == '.ds_store':
                        logger.info(f"⏭️ STEP_11: Skipping .DS_Store file: {item.name}")
                        continue
                    if item.suffix.lower() == '.mov':
                        logger.info(f"⏭️ STEP_11: Skipping .mov file: {item.name}")
                        continue
                    if item.stat().st_size > 1024 * 1024 * 1024:  # 1GB limit
                        logger.info(f"⏭️ STEP_11: Skipping large file (>1GB): {item.name}")
                        continue
                    
                    # Upload all files in Stems folder and subfolders (except excluded types)
                    should_upload = False
                    if is_stems_folder:
                        # Inside Stems folder - upload ALL files except excluded ones
                        should_upload = True
                        logger.info(f"📤 STEP_11: Found file in Stems folder/subfolder: {item.name}")
                    elif not is_stems_folder:
                        # Upload all files in root level folders (Stems, AE)
                        should_upload = True
                    
                    if should_upload:
                        try:
                            # FIXED: Upload file with Shared Drive support
                            file_metadata = {'name': item.name, 'parents': [parent_id]}
                            media = MediaFileUpload(str(item), resumable=True)
                            uploaded_file = service.files().create(
                                body=file_metadata,
                                media_body=media,
                                fields='id',
                                supportsAllDrives=True,  # CRITICAL: Support for Shared Drives
                                supportsTeamDrives=True   # CRITICAL: Legacy support
                            ).execute()
                            logger.info(f"📤 STEP_11: Uploaded file: {item.name}")
                            uploaded_count += 1
                        except Exception as e:
                            logger.error(f"❌ STEP_11: Failed to upload {item.name}: {e}")
                            continue
                    else:
                        logger.info(f"⏭️ STEP_11: Skipping file (not in Stems): {item.name}")
                        
                elif item.is_dir():
                    # FIXED: Handle directory recursion properly
                    should_recurse = False
                    new_is_stems_folder = is_stems_folder
                    
                    if not is_stems_folder:
                        # Root level - only include Stems and AE folders
                        if item.name.lower() == 'stems' or (item.name.upper() == 'AE' and get_folder_size(item) <= 1024 * 1024 * 1024):
                            should_recurse = True
                            if item.name.lower() == 'stems':
                                new_is_stems_folder = True
                    else:
                        # Inside Stems - recurse into all subfolders
                        should_recurse = True
                        new_is_stems_folder = True
                        
                    if should_recurse:
                        try:
                            # FIXED: Create subfolder with Shared Drive support
                            subfolder_metadata = {
                                'name': item.name,
                                'mimeType': 'application/vnd.google-apps.folder',
                                'parents': [parent_id]
                            }
                            subfolder = service.files().create(
                                body=subfolder_metadata,
                                fields='id',
                                supportsAllDrives=True,  # CRITICAL: Support for Shared Drives
                                supportsTeamDrives=True   # CRITICAL: Legacy support
                            ).execute()
                            subfolder_id = subfolder.get('id')
                            logger.info(f"📁 STEP_11: Created subfolder: {item.name}")
                            # FIXED: Recursively upload subfolder contents with proper Stems tracking
                            uploaded_count += upload_folder_recursive(str(item), subfolder_id, new_is_stems_folder)
                        except Exception as e:
                            logger.error(f"❌ STEP_11: Failed to create subfolder {item.name}: {e}")
                            continue
                    else:
                        logger.info(f"⏭️ STEP_11: Skipping folder: {item.name}")
            return uploaded_count
        
        def get_folder_size(folder_path):
            total = 0
            for path in folder_path.rglob('*'):
                if path.is_file():
                    try:
                        total += path.stat().st_size
                    except (OSError, FileNotFoundError):
                        continue
            return total
        
        # Start recursive upload
        total_uploaded = upload_folder_recursive(local_folder, folder_id, False)
        
        # Generate shareable link
        folder_link = f"https://drive.google.com/drive/folders/{folder_id}"
        
        success_msg = f"Successfully uploaded {total_uploaded} files to {drive_name}"
        logger.info(f"✅ STEP_11: {success_msg}")
        
        # Update Google Sheet with success status
        update_google_sheet_status(issue_id, 'AE', 'Yes')  # Google Drive Upload
        update_google_sheet_status(issue_id, 'AF', folder_name)  # Stems Folder Name
        update_google_sheet_status(issue_id, 'AG', folder_link)  # Stems Drive Link
        
        return folder_name, folder_link
        
    except Exception as e:
        error_msg = f"Error uploading to Google Drive: {e}"
        logger.error(f"❌ STEP_11: {error_msg}")
        
        # Update Google Sheet with failure status
        update_google_sheet_status(issue_id, 'AE', 'No')
        update_google_sheet_status(issue_id, 'AF', 'Failed')
        update_google_sheet_status(issue_id, 'AG', 'Failed')
        
        return None, None


def update_jira_attachment_path(issue_id, drive_link, ocd_key=None):
    """Step 12: Update Jira with Google Drive link - FIXED to update OCD ticket"""
    try:
        if drive_link == "Not uploaded":
            logger.info("⏭️ STEP_12: Skipping Jira update - no Google Drive upload")
            return True
        
        # CRITICAL FIX: Use OCD ticket key instead of VP ticket
        if ocd_key:
            target_ticket = ocd_key
            logger.info(f"🔗 STEP_12: Updating OCD ticket {target_ticket} with Drive link")
        else:
            # Fallback: Get OCD key from VP ticket details
            logger.info(f"🔍 STEP_12: Getting OCD key from VP ticket {issue_id}")
            details = get_issue_details_from_jira(issue_id)
            if details and details.get('ocd_number'):
                target_ticket = f"OCD-{details['ocd_number']}"
                logger.info(f"🔗 STEP_12: Found linked OCD ticket {target_ticket}")
            else:
                logger.error(f"❌ STEP_12: No OCD ticket found for VP {issue_id}")
                return False
        
        url = f"{JIRA_BASE_URL}/rest/api/2/issue/{target_ticket}"
        payload = {
            "fields": {
                "customfield_106811": drive_link  # Attachment Path field
            }
        }
        
        response = requests.put(
            url,
            headers=get_jira_headers(),
            json=payload,
            verify=False,
            timeout=30
        )
        
        if response.status_code in [200, 204]:
            logger.info(f"✅ STEP_12: Successfully updated OCD ticket {target_ticket} with Drive link")
            return True
        else:
            logger.error(f"❌ STEP_12: Failed to update OCD ticket {target_ticket}: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ STEP_12: Error updating Jira attachment path: {e}")
        return False


def move_to_final_destination(working_folder, access_type, video_type, title, date, backup_type, duration, language, issue_id):
    """Step 13: FIXED - Move folder to final destination with enhanced naming"""
    try:
        # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
        working_folder = working_folder.replace('\\\\', '/')
    
        logger.info(f"🚚 STEP_13: Moving folder to final destination")
        working_path = Path(working_folder)
        
        # Determine destination based on access type
        if access_type.lower() == 'public':
            destination_base = Path(VIDEO_EDIT_PUBLIC_PATH)
            logger.info(f"📁 STEP_13: Using PUBLIC destination: {destination_base}")
        else:
            destination_base = Path(VIDEO_EDIT_PRIVATE_PATH)
            logger.info(f"📁 STEP_13: Using PRIVATE destination: {destination_base}")
        
        # Create destination directory
        os.makedirs(destination_base, exist_ok=True)
        
        # ENHANCED: Create new folder name with video_type: <video_type>_<Title>_<Date>_<Language>_<Duration>_<BackupType>
        safe_title = title.replace(' ', '-').replace('/', '_').replace('\\', '_')
        safe_video_type = video_type.replace(' ', '_').replace('/', '_').replace('\\', '_')
        
        # Build folder name with conditional language inclusion
        if language and language.lower() != "not applicable":
            safe_language = language.replace(' ', '_').replace('/', '_').replace('\\', '_')
            new_folder_name = f"{safe_video_type}_{safe_title}_{date}_{safe_language}_{duration}_{backup_type}"
        else:
            new_folder_name = f"{safe_video_type}_{safe_title}_{date}_{duration}_{backup_type}"
        
        final_destination = destination_base / new_folder_name
        
        logger.info(f"📂 STEP_13: Generated folder name: {new_folder_name}")
        
        # Remove existing if exists
        if final_destination.exists():
            shutil.rmtree(final_destination)
            logger.info(f"🗑️ STEP_13: Removed existing folder at destination")
        
        # Move folder
        shutil.move(str(working_path), str(final_destination))
        
        success_msg = f"Successfully moved to {final_destination}"
        logger.info(f"✅ STEP_13: {success_msg}")
        
        # Update Google Sheet
        update_google_sheet_status(issue_id, 'AI', str(final_destination))
        
        return str(final_destination)
        
    except Exception as e:
        error_msg = f"Error moving folder to final destination: {e}"
        logger.error(f"❌ STEP_13: {error_msg}")
        
        # Update Google Sheet
        update_google_sheet_status(issue_id, 'AI', 'Failed')
        
        return None


def close_jira_ticket(issue_id, final_folder_name, video_type, title, date, backup_type, duration, language):
    """Step 14: FIXED - Close Jira ticket, update status, and populate IE_Video File Names field"""
    try:
        logger.info(f"🎫 STEP_14: Closing Jira ticket {issue_id}")
        
        # Generate IE_Video File Names value: <video_type>_<Title>_<Date>_<Language>_<Duration>_<BackupType>
        safe_title = title.replace(' ', '-').replace('/', '_').replace('\\', '_')
        safe_video_type = video_type.replace(' ', '_').replace('/', '_').replace('\\', '_')
        
        # Build IE video filename with conditional language inclusion
        if language and language.lower() != "not applicable":
            safe_language = language.replace(' ', '_').replace('/', '_').replace('\\', '_')
            ie_video_filename = f"{safe_video_type}_{safe_title}_{date}_{safe_language}_{duration}_{backup_type}"
        else:
            ie_video_filename = f"{safe_video_type}_{safe_title}_{date}_{duration}_{backup_type}"
        
        logger.info(f"🏷️ STEP_14: Generated IE_Video File Names: {ie_video_filename}")
        
        # Update status to "Edited Video Shared to Ar"
        transition_url = f"{JIRA_BASE_URL}/rest/api/2/issue/{issue_id}/transitions"
        
        # First, get available transitions
        transitions_response = requests.get(
            transition_url,
            headers=get_jira_headers(),
            verify=False,
            timeout=30
        )
        
        if transitions_response.status_code != 200:
            error_msg = f"Could not get transitions for {issue_id}"
            logger.error(f"❌ STEP_14: {error_msg}")
            update_google_sheet_status(issue_id, 'AJ', 'No')
            return False
        
        transitions = transitions_response.json().get('transitions', [])
        target_transition_id = None
        
        # Look for transition containing "Edited Video Shared to Ar"
        for transition in transitions:
            transition_name = transition.get('name', '').lower()
            if 'edited video shared to ar' in transition_name or 'edited' in transition_name:
                target_transition_id = transition.get('id')
                logger.info(f"🎯 STEP_14: Found transition: {transition.get('name')} (ID: {target_transition_id})")
                break
        
        if not target_transition_id:
            logger.warning(f"⚠️ STEP_14: Target transition not found, using first available")
            if transitions:
                target_transition_id = transitions[0].get('id')
        
        if target_transition_id:
            # Execute transition with IE_Video File Names field population
            transition_payload = {
                "transition": {"id": target_transition_id},
                "fields": {
                    "customfield_108114": ie_video_filename  # IE_Video File Names field
                },
                "update": {
                    "comment": [{
                        "add": {
                            "body": f"✅ Video processing completed successfully!\n\n" +
                                   f"📂 Final folder name: {final_folder_name}\n" +
                                   f"🎬 IE Video filename: {ie_video_filename}\n" +
                                   f"📅 Processed on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
                                   f"🎯 Video type: {video_type}\n" +
                                   f"📝 Title: {title}\n" +
                                   f"🌐 Language: {language}\n" +
                                   f"⏱️ Duration: {duration}"
                        }
                    }]
                }
            }
            
            transition_response = requests.post(
                transition_url,
                headers=get_jira_headers(),
                json=transition_payload,
                verify=False,
                timeout=30
            )
            
            if transition_response.status_code in [200, 204]:
                success_msg = f"Successfully transitioned ticket and populated IE_Video File Names"
                logger.info(f"✅ STEP_14: {success_msg}")
                
                # Update Google Sheet
                update_google_sheet_status(issue_id, 'AJ', 'Yes')
                
                return True
            else:
                error_msg = f"Failed to transition ticket: HTTP {transition_response.status_code}"
                logger.error(f"❌ STEP_14: {error_msg}")
                logger.error(f"❌ STEP_14: Response: {transition_response.text}")
                
                # Update Google Sheet
                update_google_sheet_status(issue_id, 'AJ', 'No')
                
                return False
        else:
            error_msg = "No suitable transition found"
            logger.error(f"❌ STEP_14: {error_msg}")
            
            # Update Google Sheet
            update_google_sheet_status(issue_id, 'AJ', 'No')
            
            return False
            
    except Exception as e:
        error_msg = f"Error closing Jira ticket: {e}"
        logger.error(f"❌ STEP_14: {error_msg}")
        
        # Update Google Sheet
        update_google_sheet_status(issue_id, 'AJ', 'No')
        
        return False


# FLASK ROUTES
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        conn = sqlite3.connect('trimsflow.db')
        cursor = conn.cursor()
        cursor.execute('SELECT password_hash, role, full_name, email FROM users WHERE username = ? AND is_active = 1', (username,))
        user_data = cursor.fetchone()
        
        if user_data and check_password_hash(user_data[0], password):
            cursor.execute('UPDATE users SET last_login = ? WHERE username = ?', (datetime.now(), username))
            conn.commit()
            user = User(username, username, user_data[1], user_data[2], user_data[3])
            login_user(user, remember=request.form.get('remember_me'))
            log_activity(username, 'LOGIN', 'User logged in successfully', request.remote_addr)
            conn.close()
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')
            log_activity(username or 'unknown', 'LOGIN_FAILED', f'Failed login attempt for {username}', request.remote_addr)
        
        conn.close()
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    log_activity(current_user.username, 'LOGOUT', 'User logged out', request.remote_addr)
    logout_user()
    return redirect(url_for('login'))

@app.route('/')
@login_required
def dashboard():
    return render_template('dashboard.html',
                         username=current_user.username,
                         role=current_user.role,
                         full_name=current_user.full_name)

@app.route('/admin')
@login_required
def admin_panel():
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('dashboard'))
    return render_template('admin.html',
                         username=current_user.username,
                         full_name=current_user.full_name)

# API ROUTES
@app.route('/api/dashboard/stats')
@login_required
def dashboard_stats():
    conn = sqlite3.connect('trimsflow.db')
    cursor = conn.cursor()
    
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    
    stats = {}
    
    try:
        tickets = fetch_real_jira_tickets()
        stats['pending'] = len(tickets)
    except:
        stats['pending'] = 0
    
    cursor.execute('SELECT COUNT(*) FROM actions WHERE action = "accepted" AND DATE(created_at) = ?', (today,))
    stats['done_today'] = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM actions WHERE action = "rejected" AND DATE(created_at) = ?', (today,))
    stats['rejected_today'] = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM actions WHERE action = "accepted" AND DATE(created_at) = ?', (yesterday,))
    stats['done_yesterday'] = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM actions WHERE action = "accepted" AND DATE(created_at) >= ?', (week_start,))
    stats['done_week'] = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM actions WHERE action = "accepted" AND DATE(created_at) >= ?', (month_start,))
    stats['done_month'] = cursor.fetchone()[0]
    
    conn.close()
    return jsonify(stats)

@app.route('/api/jira/issues')
@login_required
def get_jira_issues():
    try:
        issues = fetch_real_jira_tickets()
        log_activity(current_user.username, 'VIEW_TICKETS', f'Viewed {len(issues)} tickets', request.remote_addr)
        return jsonify(issues)
    except Exception as e:
        logger.error(f"Error fetching issues: {e}")
        return jsonify({'error': str(e)}), 500

# FIXED: Browse Folders API Route
@app.route('/api/browse-folders', defaults={'folder_path': ''}, methods=['GET'])
@app.route('/api/browse-folders/', defaults={'folder_path': ''}, methods=['GET'])
@app.route('/api/browse-folders/<path:folder_path>', methods=['GET'])
@login_required
def browse_folders(folder_path=''):
    try:
        logger.info(f"🔍 API browse_folders called with path: '{folder_path}'")
        
        # Use the corrected browse function
        result = browse_server_directory_fixed(folder_path)
        response = make_response(jsonify(result))
        response.headers['Content-Type'] = 'application/json'
        response.headers['Cache-Control'] = 'no-cache'
        
        logger.info(f"✅ Returning {len(result.get('folders', []))} folders and {len(result.get('files', []))} files")
        return response
        
    except Exception as e:
        logger.error(f"❌ Error in browse_folders API: {e}")
        error_response = make_response(jsonify({
            'error': str(e),
            'current_path': '',
            'absolute_path': SERVER_ROOT_PATH,
            'folders': [],
            'files': []
        }))
        error_response.headers['Content-Type'] = 'application/json'
        return error_response, 500

@app.route('/api/issues/<issue_id>/details')
@login_required
def get_issue_details_api(issue_id):
    try:
        details = get_issue_details_from_jira(issue_id)
        if not details:
            return jsonify({'error': 'Issue not found'}), 404
        log_activity(current_user.username, 'VIEW_ISSUE_DETAILS', f'Viewed details for {issue_id}', request.remote_addr)
        return jsonify(details)
    except Exception as e:
        logger.error(f"Error fetching issue details: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues/<issue_id>/jira-fields')
@login_required
def get_jira_fields_api(issue_id):
    """Get additional JIRA fields for display in modal"""
    try:
        logger.info(f"Fetching additional JIRA fields for issue: {issue_id}")
        url = f"{JIRA_BASE_URL}/rest/api/2/issue/{issue_id}"
        params = {
            'fields': 'customfield_109642,customfield_12205,customfield_109335,customfield_12207'
        }

        response = requests.get(url, headers=get_jira_headers(), params=params, verify=False, timeout=30)
        if response.status_code == 200:
            issue = response.json()
            fields = issue.get('fields', {})

            jira_fields = {
                'vertical': extract_custom_field_value(fields.get('customfield_109642', '')),
                'vp_audience_type': extract_custom_field_value(fields.get('customfield_12205', '')),
                'vp_trimbackup_folder_name': extract_custom_field_value(fields.get('customfield_109335', '')),
                'social_media_platform': extract_custom_field_value(fields.get('customfield_12207', ''))
            }

            log_activity(current_user.username, 'VIEW_JIRA_FIELDS', f'Viewed JIRA fields for {issue_id}', request.remote_addr)
            return jsonify(jira_fields)
        else:
            logger.error(f"Failed to fetch JIRA fields: HTTP {response.status_code}")
            return jsonify({'error': 'Failed to fetch JIRA fields'}), response.status_code
    except Exception as e:
        logger.error(f"Error fetching JIRA fields: {e}")
        return jsonify({'error': str(e)}), 500

# ENHANCED: Folder-based Duration Detection API
@app.route('/api/detect-duration', methods=['POST', 'OPTIONS'])
@login_required
def detect_video_duration():
    """ENHANCED: Video duration detection API using optimized detection"""
    logger.info(f"🎥 VIDEO_DURATION | Request received: {request.method} from {request.remote_addr}")
    
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        logger.info("🎥 VIDEO_DURATION | Handling OPTIONS preflight request")
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response
    
    logger.info(f"🎥 VIDEO_DURATION | POST request - User: {current_user.username} Role: {current_user.role}")
    
    try:
        # Check if we have source_folder in JSON data (for folder-based detection)
        data = request.get_json()
        if data and 'source_folder' in data:
            source_folder = data.get('source_folder', '')

            # CRITICAL FIX: Convert Windows backslashes to Linux forward slashes
            source_folder = source_folder.replace('\\', '/')

            logger.info(f"🎥 ENHANCED: Folder-based duration detection for: '{source_folder}'")
            
            if not source_folder:
                logger.error("❌ No source folder provided")
                return jsonify({'success': False, 'error': 'Source folder is required'}), 400
            
            # Use folder-based duration detection
            duration = get_video_duration_from_folder_optimized(source_folder)
            
            # Log the result
            if duration != "00Min-00Secs":
                log_activity(
                    current_user.username,
                    'DETECT_DURATION_SUCCESS',
                    f'Duration detected for {source_folder}: {duration}',
                    request.remote_addr
                )
                logger.info(f"✅ ENHANCED: Successfully detected duration: {duration}")
                return jsonify({'success': True, 'duration': duration})
            else:
                log_activity(
                    current_user.username,
                    'DETECT_DURATION_FAILED',
                    f'Failed to detect duration for {source_folder}',
                    request.remote_addr
                )
                logger.warning(f"⚠️ ENHANCED: Failed to detect duration for: {source_folder}")
                return jsonify({'success': False, 'error': 'Could not detect video duration'})
        else:
            logger.error("❌ VIDEO_DURATION | No source_folder in request data")
            return jsonify({'success': False, 'error': 'No source folder provided'})
            
    except Exception as e:
        logger.error(f"❌ VIDEO_DURATION | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': f'Server error: {str(e)}'}), 500

# CRITICAL: COMPLETE Processing Workflow with All 14 Steps and FIXED Function Calls
@app.route('/api/issues/<issue_id>/process', methods=['POST'])
@login_required
def process_issue(issue_id):
    """CRITICAL: Asynchronous processing workflow with real-time progress updates"""
    try:
        data = request.get_json()
        
        # FIXED: Capture current user info BEFORE background thread
        user_info = {
            'username': current_user.username,
            'user_id': current_user.id,
            'role': current_user.role,
            'remote_addr': request.remote_addr
        }
        
        logger.info(f"🚀 PROCESS_ISSUE: Starting comprehensive processing for {issue_id} by {user_info['username']}")

        # Initialize processing state
        processing_states[issue_id] = {
            'status': 'running',
            'current_step': 0,
            'total_steps': 14,
            'step_name': 'Initializing...',
            'progress': 0,
            'error': None,
            'logs': [],
            'start_time': time.time()
        }

        # Start processing in background thread with user context
        def background_processing():
            try:
                # FIXED: Pass user_info to background function
                with app.app_context():
                    process_issue_background(issue_id, data, user_info)
            except Exception as e:
                logger.error(f"❌ BACKGROUND_PROCESSING: Error for {issue_id}: {str(e)}")
                processing_states[issue_id].update({
                    'status': 'error',
                    'error': str(e),
                    'step_name': f'Error: {str(e)}'
                })

        # Submit to thread pool
        executor.submit(background_processing)

        # Return immediately to allow polling
        return jsonify({
            'success': True,
            'message': 'Processing started successfully',
            'issue_id': issue_id
        })

    except Exception as e:
        processing_states[issue_id] = {
            'status': 'error',
            'error': str(e)
        }
        logger.error(f"❌ PROCESS_ISSUE: Critical error processing {issue_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Processing failed: {str(e)}'
        }), 500


def process_issue_background(issue_id, data, user_info):
    """FIXED: Background processing function with proper Flask context"""
    try:
        # Extract all required data
        current_folder = data.get('source_folder', '')
        ocd_number = data.get('ocd_number', '')
        filename = data.get('filename', '')
        title = data.get('title', '')
        date = data.get('date', '')
        backup_type = data.get('backup_type', '')
        duration = data.get('duration', '')
        access_type = data.get('access_type', '')
        google_drive_upload = data.get('upload_to_drive', '')
        language = data.get('language', '')
        video_type = data.get('type_of_video', '')

        # FIXED: Helper function for smooth progress updates with console logging
        def update_progress(step_num, step_name, details=""):
            progress_percentage = int((step_num / 14) * 100)
            processing_states[issue_id].update({
                'current_step': step_num,
                'step_name': step_name,
                'progress': progress_percentage
            })
            
            # Add to logs for console display
            log_entry = f"Step {step_num}/14: {step_name}"
            if details:
                log_entry += f" - {details}"
            
            if 'logs' not in processing_states[issue_id]:
                processing_states[issue_id]['logs'] = []
            
            processing_states[issue_id]['logs'].append({
                'timestamp': time.time(),
                'message': log_entry,
                'level': 'info'
            })
            
            logger.info(f"📊 STEP_{step_num}: {step_name} - {progress_percentage}%")
            # Add small delay for smoother progress visualization
            time.sleep(1.0)

        # STEP 0: Append to Google Sheets
        update_progress(1, '📊 Saving to Google Sheets...', f'Recording VP {issue_id} processing data')
        sheet_data = {
            'vp_number': issue_id,
            'ocd_number': ocd_number,
            'source_folder': current_folder,
            'editor': data.get('editor', ''),
            'google_drive_upload': google_drive_upload,
            'title': title,
            'duration': duration,
            'backup_type': backup_type,
            'video_type': video_type,
            'date': date,
            'language': language,
            'access_type': access_type,
            'filename': filename,
            'project_assignee': data.get('project_assignee', ''),
        }

        sheet_success = append_processed_issue_to_google_sheet(sheet_data)
        if not sheet_success:
            processing_states[issue_id].update({
                'status': 'error',
                'error': 'Failed to save to Google Sheets',
                'step_name': '❌ Google Sheets Error'
            })
            return

        # Execute all 14 processing steps
        step_results = {}

        # STEP 1: Copy folder to Working Area
        update_progress(2, '📁 Copying folder to Working Area...', f'Transferring {os.path.basename(current_folder)}')
        copied_folder = copy_folder_to_working_area(current_folder, filename)
        step_results['step1'] = copied_folder is not None
        update_google_sheet_status(issue_id, 'P', 'Yes' if copied_folder else 'No')

        if not copied_folder:
            processing_states[issue_id].update({
                'status': 'error',
                'error': 'Failed to copy folder',
                'step_name': '❌ Copy Folder Error'
            })
            return

        current_folder = copied_folder

        # STEP 2: Rename folder
        update_progress(3, '✏️ Renaming copied folder...', f'Applying naming convention: {filename}')
        renamed_folder = rename_folder_in_working_area(current_folder, filename)
        step_results['step2'] = renamed_folder is not None
        update_google_sheet_status(issue_id, 'Q', 'Yes' if renamed_folder else 'No')

        if renamed_folder:
            current_folder = renamed_folder

        # STEP 3: Normalize Output folder
        update_progress(4, '🗂️ Normalizing Output folder...', 'Organizing folder structure')
        output_folder = normalize_output_folder(current_folder)
        step_results['step3'] = output_folder is not None
        update_google_sheet_status(issue_id, 'R', 'Yes' if output_folder else 'No')

        # STEP 4: Rename video file
        update_progress(5, '🎬 Renaming video file...', f'Converting to OCD format: {ocd_number}')
        if output_folder:
            video_file = rename_video_file_to_ocd(output_folder, ocd_number)
            step_results['step4'] = video_file is not None
            update_google_sheet_status(issue_id, 'S', 'Yes' if video_file else 'No')
        else:
            step_results['step4'] = False
            update_google_sheet_status(issue_id, 'S', 'No')

        # STEP 5: Normalize Stems folder
        update_progress(6, '📁 Normalizing Stems folder...')
        stems_folder = normalize_stems_folder(current_folder)
        step_results['step5'] = stems_folder is not None
        update_google_sheet_status(issue_id, 'T', 'Yes' if stems_folder else 'No')

        # STEP 6: Rename stems files
        update_progress(7, '🏷️ Renaming stems files...')
        if stems_folder:
            stems_renamed = rename_stems_files(stems_folder)
            step_results['step6'] = stems_renamed
            update_google_sheet_status(issue_id, 'U', 'Yes' if stems_renamed else 'No')
        else:
            step_results['step6'] = False
            update_google_sheet_status(issue_id, 'U', 'No')

        # STEP 7: Normalize ILP/GLP folders
        update_progress(8, '📁 Normalizing ILP/GLP folders...')
        ilp_glp_folder = normalize_ilp_glp_folders(current_folder, ocd_number)
        step_results['step7'] = ilp_glp_folder is not None
        update_google_sheet_status(issue_id, 'V', 'Yes' if ilp_glp_folder else 'No')

        # STEP 8: Copy SRT file
        update_progress(9, '📄 Copying SRT file...')
        if stems_folder:
            srt_copied = copy_srt_file_to_stems(current_folder, stems_folder, ocd_number)
            step_results['step8'] = srt_copied
            update_google_sheet_status(issue_id, 'W', 'Yes' if srt_copied else 'No')
        else:
            step_results['step8'] = False
            update_google_sheet_status(issue_id, 'W', 'No')

        # STEP 9: Validate copied folder
        update_progress(10, '🔍 Validating copied folder...')
        validation_result = validate_copied_folder(data.get('source_folder', ''), current_folder)
        step_results['step9'] = validation_result
        update_google_sheet_status(issue_id, 'X', 'Yes' if validation_result else 'No')

        # STEP 10: Extract audio files
        update_progress(11, '🎵 Extracting audio files...')
        if output_folder:
            audio_extracted = extract_audio_files(output_folder, filename)
            step_results['step10'] = audio_extracted
            update_google_sheet_status(issue_id, 'Y', 'Yes' if audio_extracted else 'No')
        else:
            step_results['step10'] = False
            update_google_sheet_status(issue_id, 'Y', 'No')

        # STEP 11: Upload to Google Drive - ENHANCED with video_type parameter
        update_progress(12, '☁️ Uploading to Google Drive...')
        folder_name, drive_link = upload_to_google_drive(
            current_folder, ocd_number, title, google_drive_upload, issue_id, video_type  # ← ADD THIS
        )

        step_results['step11'] = drive_link is not None

        # STEP 12: Update Jira with Drive link - FIXED to update OCD ticket
        update_progress(13, '🔗 Updating Jira with Google Drive link...')
        # Get OCD key from the data we already have
        ocd_key = f"OCD-{ocd_number}" if ocd_number else None
        jira_updated = update_jira_attachment_path(issue_id, drive_link or "Not uploaded", ocd_key)
        step_results['step12'] = jira_updated
        update_google_sheet_status(issue_id, 'AA', 'Yes' if jira_updated else 'No')


        # STEP 13: Move to final destination - FIXED CALL with video_type and issue_id parameters
        update_progress(14, '🚚 Moving to final destination...')
        final_destination = move_to_final_destination(
            current_folder, access_type, video_type, title, date, backup_type, duration, language, issue_id
        )
        step_results['step13'] = final_destination is not None
        update_google_sheet_status(issue_id, 'AB', 'Yes' if final_destination else 'No')

        # STEP 14: Close Jira ticket - FIXED CALL with all required parameters
        update_progress(15, '🎫 Closing Jira ticket...')
        final_folder_name = os.path.basename(final_destination) if final_destination else filename
        jira_closed = close_jira_ticket(
            issue_id, final_folder_name, video_type, title, date, backup_type, duration, language
        )
        step_results['step14'] = jira_closed
        update_google_sheet_status(issue_id, 'AC', 'Yes' if jira_closed else 'No')

        # FINAL: Mark as complete with smooth 100% progress
        processing_states[issue_id].update({
            'status': 'complete',
            'current_step': 15,
            'step_name': '🎉 Processing Complete!',
            'progress': 100,
            'step_results': step_results,
            'final_destination': final_destination,
            'drive_link': drive_link
        })

        # FIXED: Save to local database with user_info
        conn = sqlite3.connect('trimsflow.db')
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO actions (issue_id, action, user_id, filename, source_folder, processing_details,
                               language, google_drive_upload, access_type, final_destination)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (issue_id, 'accepted', user_info['username'], filename, data.get('source_folder', ''),
              json.dumps(data), language, google_drive_upload, access_type, final_destination))
        conn.commit()
        conn.close()

        # FIXED: Log successful processing with user_info (no Flask context needed)
        logger.info(f"🎉 PROCESS_ISSUE: Successfully completed all processing for {issue_id} by {user_info['username']}")
        logger.info(f"📊 FINAL RESULTS: Google Sheets: {sheet_success}, Local DB: True, Steps: 14/14, Drive Link: {drive_link}")

    except Exception as e:
        processing_states[issue_id] = {
            'status': 'error',
            'error': str(e),
            'step_name': f'❌ Error: {str(e)}'
        }
        logger.error(f"❌ PROCESS_ISSUE: Critical error processing {issue_id}: {str(e)}")


@app.route('/api/issues/<issue_id>/processing-status')
@login_required
def get_processing_status(issue_id):
    """Get real-time processing status"""
    try:
        status = processing_states.get(issue_id, {
            'status': 'idle',
            'current_step': 0,
            'total_steps': 14,
            'step_name': '',
            'progress': 0,
            'error': None
        })
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting processing status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues/<issue_id>/reject', methods=['POST'])
@login_required
def reject_issue(issue_id):
    try:
        data = request.get_json()
        reason = data.get('reason', '')
        
        if not reason:
            return jsonify({'error': 'Reason is required'}), 400
        
        conn = sqlite3.connect('trimsflow.db')
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO actions (issue_id, action, reason, user_id)
            VALUES (?, ?, ?, ?)
        ''', (issue_id, 'rejected', reason, current_user.username))
        conn.commit()
        conn.close()
        
        log_activity(current_user.username, 'REJECT_ISSUE', f'Rejected {issue_id}: {reason}', request.remote_addr)
        return jsonify({'success': True, 'message': 'Issue rejected successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues/<issue_id>/audio-code', methods=['POST'])
@login_required
def fetch_audio_code(issue_id):
    try:
        client = get_google_sheets_client()
        if not client:
            return jsonify({'error': 'Cannot connect to Google Sheets'}), 500
        
        sheet = client.open_by_key(GOOGLE_SHEETS_ID).worksheet(GOOGLE_SHEETS_TAB)
        
        col_b_values = sheet.col_values(2)
        col_a_values = sheet.col_values(1)
        
        for i, value in enumerate(col_b_values):
            if not value:
                if i < len(col_a_values):
                    audio_code = col_a_values[i]
                    sheet.update_cell(i + 1, 2, "Blocked")
                    log_activity(current_user.username, 'FETCH_AUDIO_CODE', f'Fetched audio code {audio_code} for {issue_id}', request.remote_addr)
                    return jsonify({'audio_code': audio_code})
        
        next_row = len(col_b_values) + 1
        if next_row <= len(col_a_values):
            audio_code = col_a_values[next_row - 1]
            sheet.update_cell(next_row, 2, "Blocked")
            return jsonify({'audio_code': audio_code})
        
        return jsonify({'error': 'No available audio codes'}), 400
    except Exception as e:
        logger.error(f"Error fetching audio code: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues/<issue_id>/generate-filename', methods=['POST'])
@login_required
def generate_filename(issue_id):
    try:
        data = request.get_json()
        
        ocd_number = data.get('ocd_number', '')
        audio_code = data.get('audio_code', '')
        type_of_video = data.get('type_of_video', '')
        title = data.get('title', '').replace(' ', '-')
        date = data.get('date', '')
        language = data.get('language', '')
        duration = data.get('duration', '')
        backup_type = data.get('backup_type', '')
        
        filename_parts = [
            f"OCD-{ocd_number}",
            audio_code,
            type_of_video,
            title,
            date
        ]
        
        if language and language != "Not Applicable":
            filename_parts.append(language)
        
        filename_parts.extend([
            duration,
            backup_type
        ])
        
        filename = "_".join(filter(None, filename_parts))
        logger.info(f"Generated filename: {filename}")
        
        return jsonify({'filename': filename})
    except Exception as e:
        logger.error(f"Error generating filename: {e}")
        return jsonify({'error': str(e)}), 500

# MAIN APPLICATION RUNNER
if __name__ == '__main__':
    init_db()
    logger.info("=" * 60)
    logger.info("🎬 ARCHIVES TRIMSFLOW - COMPLETE PRODUCTION APPLICATION")
    logger.info("=" * 60)
    logger.info(f"🔧 PyMediaInfo Available: {PYMEDIAINFO_AVAILABLE}")
    logger.info(f"📊 Google Sheets ID: {TRIMS_DATABASE_SHEET_ID}")
    logger.info(f"⚙️ Processing Functions: All 14 Steps Loaded and FIXED")
    logger.info(f"🚀 Optimizations: File timeout fixes, Path normalization, Thread pool")
    logger.info(f"🌐 APIs: Browse folders, Duration detection, Complete processing workflow")
    logger.info(f"✅ FIXED: Google Drive Shared Drive support with proper parameters")
    logger.info(f"✅ FIXED: Function calls with issue_id, video_type parameters")
    logger.info(f"✅ FIXED: IE_Video File Names field population in JIRA")
    logger.info("=" * 60)
    logger.info("🚀 ARCHIVES TRIMSFLOW READY FOR PRODUCTION 🚀")
    logger.info("=" * 60)
    
    try:
        app.run(host='0.0.0.0', port=5015, debug=False, threaded=True)
    except Exception as e:
        logger.error(f"Critical error starting application: {e}")
