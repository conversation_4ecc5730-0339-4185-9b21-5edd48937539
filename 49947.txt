D:\backup archives trims flow\New folder (2)\extras>python check.py
Enter VP number (e.g., VP-49714): vp-49947
{
    "expand": "renderedFields,names,schema,transitions,operations,editmeta,changelog,versionedRepresentations",
    "id": "784197",
    "self": "https://servicedesk.isha.in/rest/api/2/issue/784197",
    "key": "VP-49947",
    "renderedFields": {
        "customfield_71100": "",
        "customfield_65100": null,
        "resolution": null,
        "customfield_112207": null,
        "customfield_110027": null,
        "lastViewed": null,
        "customfield_100102": "",
        "customfield_100101": "",
        "customfield_108174": null,
        "labels": null,
        "customfield_36900": null,
        "customfield_109700": null,
        "customfield_107521": null,
        "customfield_63704": null,
        "customfield_11702": null,
        "customfield_107520": "Who Is <PERSON><PERSON><PERSON><PERSON> <PERSON>?",
        "customfield_11704": "",
        "aggregatetimeoriginalestimate": "0h",
        "customfield_58003": null,
        "customfield_107524": null,
        "customfield_10618": "",
        "customfield_10619": "",
        "issuelinks": null,
        "assignee": null,
        "customfield_63700": "",
        "customfield_110370": null,
        "customfield_63703": null,
        "customfield_23600": null,
        "components": null,
        "customfield_108400": null,
        "customfield_10605": null,
        "customfield_10606": null,
        "customfield_108529": null,
        "customfield_109859": null,
        "customfield_64701": "14/Aug/25 8:17 PM",
        "subtasks": null,
        "customfield_110708": null,
        "customfield_110707": null,
        "reporter": null,
        "customfield_53800": "",
        "customfield_107662": "",
        "customfield_107663": null,
        "customfield_37102": null,
        "progress": null,
        "customfield_69703": null,
        "customfield_108518": null,
        "votes": null,
        "worklog": {
            "startAt": 0,
            "maxResults": 20,
            "total": 0,
            "worklogs": []
        },
        "customfield_110396": "",
        "archivedby": null,
        "customfield_65900": null,
        "customfield_65903": "05/Aug/25 5:30 AM",
        "customfield_65902": "",
        "issuetype": null,
        "customfield_67202": null,
        "project": null,
        "customfield_12211": "",
        "customfield_12210": null,
        "customfield_12213": "",
        "customfield_12212": "",
        "customfield_12214": "",
        "customfield_109513": null,
        "customfield_109876": null,
        "customfield_12205": null,
        "customfield_109875": null,
        "customfield_12207": null,
        "customfield_109517": null,
        "customfield_102201": "01/Oct/25",
        "customfield_109515": null,
        "customfield_107699": null,
        "resolutiondate": "01/Oct/25 12:36 PM",
        "customfield_60001": null,
        "customfield_102202": "&#39;/Volumes/vnas2/epub/Content Review/<a href=\"/browse/OCD-20113\" title=\"[VP] [IOS] How to Experience the Real Sadhguru?\" class=\"issue-link\" data-issue-key=\"OCD-20113\"><strike>OCD-20113</strike></a>&#39;",
        "customfield_109514": null,
        "customfield_83801": null,
        "watches": null,
        "customfield_12202": "IOS 9<br/>\n<br/>\nEpub<br/>\n<br/>\nHow to Experience the Real Sadhguru?<br/>\n<br/>\nE-media suggested title<br/>\n<br/>\nEpub suggested title<br/>\n<br/>\nContent<br/>\n<br/>\nM531 <br/>\n<br/>\nScript<br/>\n<br/>\nQuestion: how to experience the real Sadhguru?<br/>\nSadhguru: So, what is it that you&#39;re calling as &quot;Sadhguru&quot;? That which can guide you beyond teachings, that which can lead you beyond logic, that which can make way for you beyond all reason because what we think is logic, what we think is our reasoning, all this can only happen within the framework of the data that we have gathered. We know a few things; using our logic we can multiply it in many different ways, but the same things.<br/>\nthe same old things will repeat in many different ways. This is what we call as karma. <br/>\nSo if you want something fresh to happen, you need a fresh injection of life which is not about what you know, it is not a philosophy, it is not an ideology, it is not a religion, it is not a belief system, it is not in the framework of your knowledge or your understanding or your present perception. If you find that – that &quot;Sadhguru&quot;. What is your prarabdha, what is the prescribed life for you? When somebody takes you beyond that, you call that person &quot;Sadhguru&quot;.<br/>\nSo... well, it&#39;s on. When it&#39;s still on – it&#39;s not going to be on forever, it&#39;s still on and I live dangerously (Laughs) – so when it&#39;s still on, make it happen for yourself. It&#39;s my wish and my blessing. Every one of you should know this.<br/>\n<br/>\nE-media comment<br/>\n<br/>\nthis is a very good concept, can we find some more profound content about this topic.<br/>\n<br/>\nE-pub comment<br/>\n<br/>\nSwami Harsha&#39;s Comment<br/>\n<br/>\nOK<br/>\n<br/>\nGCP comment<br/>\n<br/>\nbeautiful content",
        "customfield_12201": null,
        "customfield_12204": null,
        "customfield_109502": null,
        "customfield_109501": "",
        "customfield_109500": "",
        "customfield_111700": null,
        "customfield_109869": null,
        "customfield_109505": null,
        "customfield_109504": null,
        "customfield_109503": null,
        "customfield_106913": null,
        "customfield_106914": null,
        "customfield_109509": "",
        "customfield_109508": null,
        "customfield_106912": null,
        "customfield_109507": null,
        "updated": "Just now",
        "customfield_106915": null,
        "customfield_63420": null,
        "timeoriginalestimate": "0h",
        "description": "<p><span class=\"error\">&#91;VP&#93;</span> <span class=\"error\">&#91;IOS&#93;</span> How to Experience the Real Sadhguru?</p>",
        "customfield_109652": null,
        "customfield_108200": null,
        "customfield_109651": null,
        "customfield_109650": null,
        "timetracking": {
            "originalEstimate": "0h",
            "remainingEstimate": "0h",
            "timeSpent": "0h",
            "originalEstimateSeconds": 0,
            "remainingEstimateSeconds": 0,
            "timeSpentSeconds": 0
        },
        "customfield_112824": null,
        "customfield_63418": null,
        "customfield_10006": "",
        "customfield_112825": null,
        "customfield_10007": "",
        "customfield_112822": null,
        "customfield_108202": "",
        "customfield_112823": null,
        "customfield_109653": null,
        "customfield_10009": null,
        "customfield_106700": null,
        "customfield_109417": null,
        "customfield_63410": "",
        "customfield_63411": null,
        "customfield_63413": null,
        "customfield_109419": null,
        "customfield_22900": null,
        "customfield_63414": null,
        "customfield_63415": null,
        "customfield_63416": null,
        "summary": null,
        "customfield_10000": null,
        "customfield_10001": null,
        "customfield_12301": null,
        "customfield_10002": null,
        "customfield_10003": null,
        "customfield_110508": null,
        "customfield_107103": null,
        "customfield_63407": null,
        "customfield_107104": null,
        "customfield_63408": "",
        "customfield_109523": "04/Oct/25",
        "environment": "",
        "customfield_109643": null,
        "customfield_63409": "",
        "customfield_79300": null,
        "customfield_109642": null,
        "customfield_109649": null,
        "customfield_111600": null,
        "customfield_109648": null,
        "customfield_106811": null,
        "duedate": "12/Oct/25",
        "customfield_109647": "05/Sep/25",
        "customfield_112137": null,
        "customfield_109646": null,
        "customfield_112134": null,
        "customfield_112135": null,
        "customfield_63400": null,
        "comment": {
            "comments": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/issue/784197/comment/3417028",
                    "id": "3417028",
                    "author": {
                        "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                        "name": "archives.bot",
                        "key": "JIRAUSER97240",
                        "emailAddress": "<EMAIL>",
                        "avatarUrls": {
                            "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                            "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                            "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                            "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                        },
                        "displayName": "Archives Bot",
                        "active": true,
                        "timeZone": "Asia/Kolkata"
                    },
                    "body": "<p>Issue reopened by admin. Reason: namaskaram,</p>",
                    "updateAuthor": {
                        "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                        "name": "archives.bot",
                        "key": "JIRAUSER97240",
                        "emailAddress": "<EMAIL>",
                        "avatarUrls": {
                            "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                            "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                            "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                            "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                        },
                        "displayName": "Archives Bot",
                        "active": true,
                        "timeZone": "Asia/Kolkata"
                    },
                    "created": "21 minutes ago",
                    "updated": "21 minutes ago"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/issue/784197/comment/3417042",
                    "id": "3417042",
                    "author": {
                        "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                        "name": "archives.bot",
                        "key": "JIRAUSER97240",
                        "emailAddress": "<EMAIL>",
                        "avatarUrls": {
                            "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                            "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                            "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                            "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                        },
                        "displayName": "Archives Bot",
                        "active": true,
                        "timeZone": "Asia/Kolkata"
                    },
                    "body": "<p>Issue reopened by admin. Reason: namaskaram</p>",
                    "updateAuthor": {
                        "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                        "name": "archives.bot",
                        "key": "JIRAUSER97240",
                        "emailAddress": "<EMAIL>",
                        "avatarUrls": {
                            "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                            "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                            "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                            "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                        },
                        "displayName": "Archives Bot",
                        "active": true,
                        "timeZone": "Asia/Kolkata"
                    },
                    "created": "5 minutes ago",
                    "updated": "5 minutes ago"
                }
            ],
            "maxResults": 2,
            "total": 2,
            "startAt": 0
        },
        "customfield_63401": null,
        "customfield_63402": "",
        "customfield_63404": "",
        "customfield_63405": "",
        "customfield_63406": null,
        "customfield_69301": "",
        "customfield_50802": null,
        "customfield_69300": "<a href=\"/browse/OCD-20113\" title=\"[VP] [IOS] How to Experience the Real Sadhguru?\" class=\"issue-link\" data-issue-key=\"OCD-20113\"><strike>OCD-20113</strike></a>_How_to_Experience_the_Real_Sadhguru_IOS_EngR",
        "customfield_69303": null,
        "customfield_69302": null,
        "fixVersions": null,
        "customfield_109552": null,
        "customfield_108100": null,
        "customfield_106722": "",
        "customfield_109439": null,
        "customfield_106723": null,
        "customfield_109438": "09/Oct/25",
        "customfield_109437": null,
        "customfield_106721": "",
        "customfield_112163": null,
        "customfield_76404": null,
        "customfield_76403": null,
        "customfield_76402": null,
        "priority": null,
        "customfield_112828": null,
        "customfield_109663": null,
        "customfield_109421": null,
        "customfield_107001": null,
        "customfield_112829": null,
        "customfield_109420": null,
        "customfield_112826": null,
        "customfield_112827": null,
        "customfield_112835": null,
        "customfield_112836": null,
        "customfield_112833": null,
        "customfield_109423": "",
        "customfield_112834": "",
        "customfield_109422": "",
        "customfield_112831": null,
        "timeestimate": "0h",
        "versions": null,
        "customfield_112832": null,
        "customfield_107006": null,
        "customfield_112830": null,
        "customfield_106710": "",
        "customfield_63421": null,
        "customfield_63422": null,
        "customfield_63423": "",
        "customfield_106713": null,
        "customfield_63425": "",
        "customfield_63426": null,
        "status": null,
        "customfield_108123": null,
        "customfield_108000": null,
        "archiveddate": null,
        "customfield_107037": null,
        "customfield_62801": "closing for trim. videonot yet approved.",
        "customfield_110204": null,
        "customfield_62800": null,
        "customfield_109335": "Z:\\_trimbackup\\Koushik anand\\<a href=\"/browse/OCD-20113\" title=\"[VP] [IOS] How to Experience the Real Sadhguru?\" class=\"issue-link\" data-issue-key=\"OCD-20113\"><strike>OCD-20113</strike></a>_How_To_Experience_Real_Sadhguru",
        "customfield_107035": null,
        "customfield_108124": null,
        "customfield_106744": null,
        "aggregatetimeestimate": "0h",
        "customfield_106743": "",
        "customfield_64302": "",
        "customfield_62802": "",
        "creator": null,
        "aggregateprogress": null,
        "customfield_107022": null,
        "customfield_10200": "",
        "customfield_11403": null,
        "customfield_109203": null,
        "customfield_108114": "",
        "customfield_109202": null,
        "customfield_107822": "",
        "customfield_109206": null,
        "customfield_65501": "",
        "customfield_65500": null,
        "customfield_63202": "14/Aug/25 8:17 PM",
        "customfield_63203": null,
        "customfield_65502": null,
        "customfield_112173": "",
        "customfield_65301": "/Volumes/vnas2/video/koushik.anand/<a href=\"/browse/OCD-20113\" title=\"[VP] [IOS] How to Experience the Real Sadhguru?\" class=\"issue-link\" data-issue-key=\"OCD-20113\"><strike>OCD-20113</strike></a>_How_to_Experience_the_Real_Sadhguru_IOS_EngR",
        "customfield_63001": null,
        "timespent": "0h",
        "aggregatetimespent": "0h",
        "customfield_10311": "",
        "customfield_108022": null,
        "customfield_10312": null,
        "customfield_108021": null,
        "customfield_108028": "",
        "customfield_107610": null,
        "customfield_107057": null,
        "customfield_108025": null,
        "customfield_10308": null,
        "customfield_109911": null,
        "customfield_107611": null,
        "customfield_10309": "",
        "customfield_107612": null,
        "customfield_68802": "",
        "workratio": null,
        "customfield_68800": null,
        "customfield_68801": null,
        "customfield_41201": "",
        "created": "01/Oct/25 12:36 PM",
        "customfield_108134": "09/Oct/25",
        "customfield_108133": null,
        "customfield_10300": null,
        "customfield_10301": "",
        "customfield_70900": null,
        "customfield_110336": "",
        "customfield_107723": "",
        "customfield_109902": null,
        "customfield_107724": null,
        "customfield_107603": null,
        "customfield_106756": null,
        "customfield_110335": null,
        "customfield_106754": null,
        "customfield_107601": "",
        "customfield_107606": null,
        "customfield_107607": null,
        "customfield_107725": null,
        "customfield_106757": null,
        "customfield_109903": null,
        "customfield_21304": null,
        "customfield_109909": null,
        "customfield_21303": null,
        "customfield_107608": null,
        "customfield_21302": null,
        "customfield_107609": null,
        "customfield_21300": null,
        "customfield_69001": null,
        "customfield_69000": null,
        "customfield_17602": "",
        "customfield_110006": null,
        "attachment": [],
        "customfield_10405": null,
        "customfield_107519": "",
        "customfield_26400": null,
        "customfield_110000": null,
        "customfield_64900": null,
        "customfield_109819": null,
        "customfield_65310": null,
        "customfield_64100": "Who Is Sadhguru Really?",
        "customfield_64101": null,
        "customfield_65311": "n/a",
        "customfield_108031": "00:01:00",
        "customfield_108030": null,
        "customfield_107620": null,
        "customfield_10512": "",
        "customfield_110355": null,
        "customfield_107625": "",
        "customfield_110356": null,
        "customfield_65303": null,
        "customfield_65302": "",
        "customfield_107629": "",
        "customfield_65305": null,
        "customfield_107626": null,
        "customfield_65304": null,
        "customfield_65307": null,
        "customfield_65308": null,
        "customfield_21401": null
    },
    "transitions": [
        {
            "id": "235",
            "name": "On Hold",
            "description": "",
            "opsbarSequence": 2147483647,
            "to": {
                "self": "https://servicedesk.isha.in/rest/api/2/status/13100",
                "description": "",
                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/generic.png",
                "name": "On Hold",
                "id": "13100",
                "statusCategory": {
                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/2",
                    "id": 2,
                    "key": "new",
                    "colorName": "default",
                    "name": "To Do"
                }
            }
        },
        {
            "id": "255",
            "name": "Edited Video Shared to Ar",
            "description": "",
            "opsbarSequence": 2147483647,
            "to": {
                "self": "https://servicedesk.isha.in/rest/api/2/status/6",
                "description": "The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.",
                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/closed.png",
                "name": "Closed",
                "id": "6",
                "statusCategory": {
                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/3",
                    "id": 3,
                    "key": "done",
                    "colorName": "success",
                    "name": "Done"
                }
            }
        },
        {
            "id": "265",
            "name": "Trim Backup Done",
            "description": "",
            "opsbarSequence": 2147483647,
            "to": {
                "self": "https://servicedesk.isha.in/rest/api/2/status/10901",
                "description": "",
                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/generic.png",
                "name": "Trim Backup Done",
                "id": "10901",
                "statusCategory": {
                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/4",
                    "id": 4,
                    "key": "indeterminate",
                    "colorName": "inprogress",
                    "name": "In Progress"
                }
            }
        },
        {
            "id": "285",
            "name": "Re-Open",
            "description": "",
            "opsbarSequence": 2147483647,
            "to": {
                "self": "https://servicedesk.isha.in/rest/api/2/status/4",
                "description": "This issue was once resolved, but the resolution was deemed incorrect. From here issues are either marked assigned or resolved.",
                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/reopened.png",
                "name": "Reopened",
                "id": "4",
                "statusCategory": {
                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/4",
                    "id": 4,
                    "key": "indeterminate",
                    "colorName": "inprogress",
                    "name": "In Progress"
                }
            }
        }
    ],
    "operations": {
        "linkGroups": [
            {
                "id": "view.issue.opsbar",
                "links": [],
                "groups": [
                    {
                        "id": "edit-issue_container",
                        "weight": 1,
                        "links": [
                            {
                                "id": "edit-issue",
                                "styleClass": "issueaction-edit-issue",
                                "iconClass": "aui-icon aui-icon-small aui-iconfont-edit",
                                "label": "Edit",
                                "title": "Edit this issue",
                                "href": "/secure/EditIssue!default.jspa?id=784197",
                                "weight": 1,
                                "params": {
                                    "iconClass": "aui-icon aui-icon-small aui-iconfont-edit"
                                }
                            }
                        ],
                        "groups": []
                    },
                    {
                        "id": "comment-issue_container",
                        "weight": 10,
                        "links": [
                            {
                                "id": "comment-issue",
                                "styleClass": "issueaction-comment-issue add-issue-comment",
                                "iconClass": "aui-icon aui-icon-small aui-iconfont-comment icon-comment",
                                "label": "Add comment",
                                "title": "Comment on this issue",
                                "href": "/secure/AddComment!default.jspa?id=784197",
                                "weight": 10,
                                "params": {
                                    "iconClass": "aui-icon aui-icon-small aui-iconfont-comment icon-comment"
                                }
                            }
                        ],
                        "groups": []
                    },
                    {
                        "id": "opsbar-operations",
                        "weight": 10,
                        "links": [
                            {
                                "id": "schedule-web-item-link",
                                "styleClass": "",
                                "label": "QR Code Generator",
                                "href": "",
                                "weight": 3,
                                "params": {}
                            }
                        ],
                        "groups": [
                            {
                                "header": {
                                    "id": "opsbar-operations_more",
                                    "label": "More"
                                },
                                "links": [],
                                "groups": [
                                    {
                                        "id": "operations-top-level",
                                        "weight": 10,
                                        "links": [
                                            {
                                                "id": "assign-issue",
                                                "styleClass": "issueaction-assign-issue",
                                                "label": "Assign",
                                                "title": "Assign this issue to someone",
                                                "href": "/secure/AssignIssue!default.jspa?id=784197",
                                                "weight": 5,
                                                "params": {}
                                            },
                                            {
                                                "id": "add-hours-on-issue",
                                                "styleClass": "tempo-open-log-work-dialog",
                                                "label": "Log Time",
                                                "title": "Log Time on this Issue with Tempo",
                                                "href": "",
                                                "weight": 20,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-work",
                                        "weight": 20,
                                        "links": [
                                            {
                                                "id": "log-work",
                                                "styleClass": "issueaction-log-work",
                                                "label": "Log work",
                                                "title": "Log work against this issue",
                                                "href": "/secure/CreateWorklog!default.jspa?id=784197",
                                                "weight": 10,
                                                "params": {}
                                            },
                                            {
                                                "id": "add-issue-expense",
                                                "styleClass": "tempo-open-expense-dialog",
                                                "label": "Add Expense",
                                                "title": "Add Expense on this Issue with Tempo",
                                                "href": "",
                                                "weight": 20,
                                                "params": {}
                                            },
                                            {
                                                "id": "plan-time-on-issue",
                                                "styleClass": "tempo-open-plan-time-dialog",
                                                "label": "Plan Time",
                                                "title": "Plan Time on this Issue with Tempo",
                                                "href": "",
                                                "weight": 21,
                                                "params": {}
                                            },
                                            {
                                                "id": "tempo-issue-tracker-start-mlink",
                                                "styleClass": "tempo-bar-start",
                                                "label": "Start Tracker",
                                                "title": "Start tracking time on this issue",
                                                "href": "",
                                                "weight": 22,
                                                "params": {}
                                            },
                                            {
                                                "id": "view-time-on-issue",
                                                "styleClass": "",
                                                "label": "View Worklogs",
                                                "title": "View Worklogs on this Issue in Tempo",
                                                "href": "/secure/Tempo.jspa#/reports/logged-time?taskKey=VP-49947",
                                                "weight": 22,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-attachments",
                                        "weight": 30,
                                        "links": [
                                            {
                                                "id": "attach-file",
                                                "styleClass": "unified-attach-file",
                                                "label": "Attach files",
                                                "title": "Attach one or more files to this issue",
                                                "href": "/secure/AttachFile!default.jspa?id=784197",
                                                "weight": 10,
                                                "params": {}
                                            },
                                            {
                                                "id": "attach-screenshot-html5",
                                                "styleClass": "issueaction-attach-screenshot-html5",
                                                "label": "Attach Screenshot",
                                                "href": "/secure/ShowAttachScreenshotFormAction!default.jspa?id=784197",
                                                "weight": 15,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-voteswatchers",
                                        "weight": 40,
                                        "links": [
                                            {
                                                "id": "view-voters",
                                                "styleClass": "issueaction-view-voters",
                                                "label": "Voters",
                                                "title": "View voters for this issue",
                                                "href": "/secure/ViewVoters!default.jspa?id=784197",
                                                "weight": 30,
                                                "params": {}
                                            },
                                            {
                                                "id": "toggle-watch-issue",
                                                "styleClass": "issueaction-watch-issue",
                                                "label": "Watch issue",
                                                "title": "Start watching this issue",
                                                "href": "/secure/VoteOrWatchIssue.jspa?atl_token=BM7Y-45G6-BET8-426R_1ec20b3f387b6ff06014b20589804a60937c8b66_lin&id=784197&watch=watch",
                                                "weight": 40,
                                                "params": {}
                                            },
                                            {
                                                "id": "manage-watchers",
                                                "styleClass": "issueaction-manage-watchers",
                                                "label": "Watchers",
                                                "title": "Manage the watchers of this issue",
                                                "href": "/secure/ManageWatchers!default.jspa?id=784197",
                                                "weight": 60,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-subtasks",
                                        "weight": 50,
                                        "links": [
                                            {
                                                "id": "create-subtask",
                                                "styleClass": "issueaction-create-subtask",
                                                "label": "Create sub-task",
                                                "title": "Create sub-task for this issue",
                                                "href": "/secure/CreateSubTaskIssue!default.jspa?parentIssueId=784197",
                                                "weight": 10,
                                                "params": {}
                                            },
                                            {
                                                "id": "issue-to-subtask",
                                                "styleClass": "issueaction-issue-to-subtask",
                                                "label": "Convert to sub-task",
                                                "title": "Convert this issue to sub-task",
                                                "href": "/secure/ConvertIssue.jspa?id=784197",
                                                "weight": 20,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-operations",
                                        "weight": 60,
                                        "links": [
                                            {
                                                "id": "create-linked-issue",
                                                "styleClass": "issueaction-create-linked-issue",
                                                "label": "Create linked issue",
                                                "href": "#",
                                                "weight": 5,
                                                "params": {}
                                            },
                                            {
                                                "id": "move-issue",
                                                "styleClass": "issueaction-move-issue",
                                                "label": "Move",
                                                "title": "Move this issue to another project or issue type.",
                                                "href": "/secure/MoveIssue!default.jspa?id=784197",
                                                "weight": 10,
                                                "params": {}
                                            },
                                            {
                                                "id": "link-issue",
                                                "styleClass": "issueaction-link-issue",
                                                "label": "Link",
                                                "title": "Link this issue to another issue or item",
                                                "href": "/secure/LinkJiraIssue!default.jspa?id=784197",
                                                "weight": 20,
                                                "params": {}
                                            },
                                            {
                                                "id": "clone-issue",
                                                "styleClass": "issueaction-clone-issue",
                                                "label": "Clone",
                                                "title": "Clone this issue",
                                                "href": "/secure/CloneIssueDetails!default.jspa?id=784197",
                                                "weight": 20,
                                                "params": {}
                                            },
                                            {
                                                "id": "edit-labels",
                                                "styleClass": "issueaction-edit-labels",
                                                "label": "Labels",
                                                "title": "Edit this issue's labels",
                                                "href": "/secure/EditLabels!default.jspa?id=784197",
                                                "weight": 30,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    },
                                    {
                                        "id": "operations-delete",
                                        "weight": 70,
                                        "links": [
                                            {
                                                "id": "delete-issue",
                                                "styleClass": "issueaction-delete-issue",
                                                "label": "Delete",
                                                "title": "Delete this issue",
                                                "href": "/secure/DeleteIssue!default.jspa?id=784197",
                                                "weight": 10,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": "opsbar-transitions",
                        "weight": 20,
                        "links": [],
                        "groups": [
                            {
                                "header": {
                                    "id": "opsbar-transitions_more",
                                    "styleClass": "opsbar-transitions__status-category_indeterminate",
                                    "label": "Trim Backup Done"
                                },
                                "links": [],
                                "groups": [
                                    {
                                        "id": "transitions-all",
                                        "weight": 70,
                                        "links": [
                                            {
                                                "id": "action_id_235",
                                                "styleClass": "issueaction-workflow-transition",
                                                "label": "On Hold",
                                                "href": "/secure/WorkflowUIDispatcher.jspa?id=784197&action=235&atl_token=",
                                                "weight": 10,
                                                "params": {}
                                            },
                                            {
                                                "id": "action_id_255",
                                                "styleClass": "issueaction-workflow-transition",
                                                "label": "Edited Video Shared to Ar",
                                                "href": "/secure/WorkflowUIDispatcher.jspa?id=784197&action=255&atl_token=",
                                                "weight": 20,
                                                "params": {}
                                            },
                                            {
                                                "id": "action_id_265",
                                                "styleClass": "issueaction-workflow-transition",
                                                "label": "Trim Backup Done",
                                                "href": "/secure/WorkflowUIDispatcher.jspa?id=784197&action=265&atl_token=",
                                                "weight": 30,
                                                "params": {}
                                            },
                                            {
                                                "id": "action_id_285",
                                                "styleClass": "issueaction-workflow-transition",
                                                "label": "Re-Open",
                                                "href": "/secure/WorkflowUIDispatcher.jspa?id=784197&action=285&atl_token=",
                                                "weight": 40,
                                                "params": {}
                                            },
                                            {
                                                "id": "view-workflow-button",
                                                "styleClass": "jira-workflow-designer-link",
                                                "label": "View workflow",
                                                "href": "/browse/VP-49947?workflowName=Video+Pub+Trim+Backup+Workflow&stepId=26",
                                                "weight": 1000000,
                                                "params": {}
                                            }
                                        ],
                                        "groups": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": "opsbar-admin",
                        "weight": 30,
                        "links": [],
                        "groups": [
                            {
                                "header": {
                                    "id": "opsbar-admin_more",
                                    "label": "Admin"
                                },
                                "links": [],
                                "groups": []
                            }
                        ]
                    },
                    {
                        "id": "opsbar-restore",
                        "weight": 40,
                        "links": [],
                        "groups": [
                            {
                                "header": {
                                    "id": "opsbar-restore_more",
                                    "label": "Restore Issue"
                                },
                                "links": [],
                                "groups": []
                            }
                        ]
                    }
                ]
            },
            {
                "id": "jira.issue.tools",
                "links": [
                    {
                        "id": "jira-share-trigger",
                        "styleClass": "viewissue-share",
                        "iconClass": "aui-icon aui-icon-small aui-iconfont-share",
                        "label": "",
                        "title": "Share this issue by emailing other users",
                        "href": "",
                        "weight": 10,
                        "params": {
                            "iconClass": "aui-icon aui-icon-small aui-iconfont-share"
                        }
                    }
                ],
                "groups": [
                    {
                        "id": "view.issue.exports",
                        "header": {
                            "id": "viewissue-export",
                            "iconClass": "icon-default aui-icon aui-icon-small aui-iconfont-export",
                            "label": "Export",
                            "title": "Export this issue in another format"
                        },
                        "links": [
                            {
                                "id": "jira.issueviews:issue-xml",
                                "label": "XML",
                                "href": "/si/jira.issueviews:issue-xml/VP-49947/VP-49947.xml"
                            },
                            {
                                "id": "jira.issueviews:issue-word",
                                "label": "Word",
                                "href": "/si/jira.issueviews:issue-word/VP-49947/VP-49947.doc"
                            },
                            {
                                "id": "jira.issueviews:issue-html",
                                "label": "Printable",
                                "href": "/si/jira.issueviews:issue-html/VP-49947/VP-49947.html"
                            }
                        ],
                        "groups": []
                    }
                ]
            }
        ]
    },
    "editmeta": {
        "fields": {
            "summary": {
                "required": true,
                "schema": {
                    "type": "string",
                    "system": "summary"
                },
                "name": "Summary",
                "fieldId": "summary",
                "operations": [
                    "set"
                ]
            },
            "issuetype": {
                "required": false,
                "schema": {
                    "type": "issuetype",
                    "system": "issuetype"
                },
                "name": "Issue Type",
                "fieldId": "issuetype",
                "operations": [],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/issuetype/15200",
                        "id": "15200",
                        "description": "",
                        "iconUrl": "https://servicedesk.isha.in/secure/viewavatar?size=xsmall&avatarId=10300&avatarType=issuetype",
                        "name": "Trim Backup Issue Type",
                        "subtask": false,
                        "avatarId": 10300
                    }
                ]
            },
            "customfield_106723": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 106723
                },
                "name": "VP_DelayReason",
                "fieldId": "customfield_106723",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30327",
                        "value": "No Delay",
                        "id": "30327",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29854",
                        "value": "Admin Delay",
                        "id": "29854",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29859",
                        "value": "Awaiting Final Approval",
                        "id": "29859",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29858",
                        "value": "Content Delay",
                        "id": "29858",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29855",
                        "value": "Editor Work Delay",
                        "id": "29855",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29851",
                        "value": "Editors not Available",
                        "id": "29851",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29844",
                        "value": "Footage Delay",
                        "id": "29844",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29857",
                        "value": "Footage Request - GC Approval",
                        "id": "29857",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29849",
                        "value": "Impression Delay",
                        "id": "29849",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29853",
                        "value": "Internal Review Delay",
                        "id": "29853",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29850",
                        "value": "Photo Pub Delay",
                        "id": "29850",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29856",
                        "value": "Request Clarifications - Waiting on User Dept",
                        "id": "29856",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29848",
                        "value": "Script Delay",
                        "id": "29848",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29852",
                        "value": "Shoot Delay",
                        "id": "29852",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29880",
                        "value": "SOI Delay",
                        "id": "29880",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29846",
                        "value": "User Dept End Slide Delay",
                        "id": "29846",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29845",
                        "value": "User Dept Review Delay",
                        "id": "29845",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29847",
                        "value": "User Dept Script Review Delay",
                        "id": "29847",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30939",
                        "value": "GCP Delay",
                        "id": "30939",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39306",
                        "value": "Late request",
                        "id": "39306",
                        "disabled": false
                    }
                ]
            },
            "customfield_10009": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 10009
                },
                "name": "Dept",
                "fieldId": "customfield_10009",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10309",
                        "value": "Accommodations Residential",
                        "id": "10309",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38751",
                        "value": "Accommodation Residential_BLR",
                        "id": "38751",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10310",
                        "value": "Adi Yogi Temple Construction",
                        "id": "10310",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33558",
                        "value": "Adiyogi_BLR",
                        "id": "33558",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36251",
                        "value": "Admin_BLR",
                        "id": "36251",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10312",
                        "value": "Akshaya",
                        "id": "10312",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33403",
                        "value": "Akshaya BIYC",
                        "id": "33403",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38754",
                        "value": "Akshaya_BLR",
                        "id": "38754",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33200",
                        "value": "Akshaya Mattumane",
                        "id": "33200",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10318",
                        "value": "Amenities",
                        "id": "10318",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38752",
                        "value": "Amenities_BLR",
                        "id": "38752",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10314",
                        "value": "Archives",
                        "id": "10314",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10313",
                        "value": "ARR",
                        "id": "10313",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33518",
                        "value": "ARR - FPO Farmer Support",
                        "id": "33518",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33886",
                        "value": "ARR - Isha Gramotsavam",
                        "id": "33886",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10311",
                        "value": "Ashram Administration",
                        "id": "10311",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10315",
                        "value": "Ashram Aesthetics",
                        "id": "10315",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10317",
                        "value": "Ashram Clinic",
                        "id": "10317",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38753",
                        "value": "Ashram Clinic_BLR",
                        "id": "38753",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10316",
                        "value": "Ashram Landscaping",
                        "id": "10316",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10319",
                        "value": "Audio",
                        "id": "10319",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33559",
                        "value": "Audio & Visual_BLR",
                        "id": "33559",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10339",
                        "value": "Audit",
                        "id": "10339",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/27002",
                        "value": "AYA Maintenance",
                        "id": "27002",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28900",
                        "value": "Bangalore Office",
                        "id": "28900",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33564",
                        "value": "Bengaluru Temple",
                        "id": "33564",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21000",
                        "value": "BRS Stay",
                        "id": "21000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10322",
                        "value": "Carpentry",
                        "id": "10322",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32600",
                        "value": "CC - Awareness",
                        "id": "32600",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32601",
                        "value": "CC - Backoffice",
                        "id": "32601",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34444",
                        "value": "CC - Nurseries - KA - SPP and DP",
                        "id": "34444",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31806",
                        "value": "CC Farmer Mobilisation - KA",
                        "id": "31806",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31805",
                        "value": "CC Farmer Mobilisation - TN",
                        "id": "31805",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34507",
                        "value": "Chamundi Prashala ",
                        "id": "34507",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31712",
                        "value": "Consecrated Temple Offerings",
                        "id": "31712",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10320",
                        "value": "Construction - CSP",
                        "id": "10320",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10323",
                        "value": "Construction - LSP",
                        "id": "10323",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36248",
                        "value": "Construction TSS",
                        "id": "36248",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31804",
                        "value": "CC - Nurseries - TN - SPP & DP",
                        "id": "31804",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33019",
                        "value": "Data science",
                        "id": "33019",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37000",
                        "value": "Design and Project Coordination",
                        "id": "37000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10324",
                        "value": "Devi Eye Project",
                        "id": "10324",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10325",
                        "value": "Dhyanalinga",
                        "id": "10325",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28000",
                        "value": "DL Prasadam",
                        "id": "28000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33904",
                        "value": "Dorm Accommodations",
                        "id": "33904",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10327",
                        "value": "E-Media",
                        "id": "10327",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10328",
                        "value": "Electrical",
                        "id": "10328",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/23200",
                        "value": "Cottage Accomodation",
                        "id": "23200",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33557",
                        "value": "Electrical_BLR",
                        "id": "33557",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10326",
                        "value": "English Publications",
                        "id": "10326",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10329",
                        "value": "Fabrication",
                        "id": "10329",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10361",
                        "value": "Finance ( Project )",
                        "id": "10361",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10400",
                        "value": "Finance (Business)",
                        "id": "10400",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36901",
                        "value": "Finance_BLR",
                        "id": "36901",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10015",
                        "value": "Foundation Finance",
                        "id": "10015",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31710",
                        "value": "FPO",
                        "id": "31710",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10330",
                        "value": "Garden",
                        "id": "10330",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36250",
                        "value": "Garden_BLR",
                        "id": "36250",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30840",
                        "value": "E-Media Panel (GEP)",
                        "id": "30840",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21400",
                        "value": "Global Language Publication",
                        "id": "21400",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/27603",
                        "value": "Earthworks",
                        "id": "27603",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34508",
                        "value": "Hall Maintenance",
                        "id": "34508",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10331",
                        "value": "Hata Yoga School",
                        "id": "10331",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10333",
                        "value": "Home School Clinic",
                        "id": "10333",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33642",
                        "value": "HP_IFBLR",
                        "id": "33642",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19002",
                        "value": "IARF",
                        "id": "19002",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10334",
                        "value": "Human Possibilities",
                        "id": "10334",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19200",
                        "value": "IBPL-Marketing",
                        "id": "19200",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22900",
                        "value": "III - USA",
                        "id": "22900",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19000",
                        "value": "IBPL-Online",
                        "id": "19000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10354",
                        "value": "Indian Language Publications",
                        "id": "10354",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33641",
                        "value": "HP_SYTBLR",
                        "id": "33641",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10342",
                        "value": "Inventory",
                        "id": "10342",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36236",
                        "value": "Inner Engineering Online",
                        "id": "36236",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10341",
                        "value": "Isha Arogya",
                        "id": "10341",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10016",
                        "value": "Isha Bhiksha",
                        "id": "10016",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10321",
                        "value": "Isha Crafts",
                        "id": "10321",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37009",
                        "value": "Isha Engineering Service Management",
                        "id": "37009",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19001",
                        "value": "ILPL-Shop",
                        "id": "19001",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10336",
                        "value": "Isha Foods & Spices Production unit",
                        "id": "10336",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10338",
                        "value": "IIIS-Research",
                        "id": "10338",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10349",
                        "value": "Isha Goushala",
                        "id": "10349",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10365",
                        "value": "Isha Health Solutions",
                        "id": "10365",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36247",
                        "value": "Isha Engineering Back Office",
                        "id": "36247",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10332",
                        "value": "Isha Home School",
                        "id": "10332",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10340",
                        "value": "Isha Impressions",
                        "id": "10340",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/27000",
                        "value": "Isha Kattupoo",
                        "id": "27000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33475",
                        "value": "Isha Life",
                        "id": "33475",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31846",
                        "value": "Isha Pharmacy (Alandurai)",
                        "id": "31846",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31847",
                        "value": "Isha Pharmacy (Muttathuvayal)",
                        "id": "31847",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31848",
                        "value": "Isha Pharmacy (Salem)",
                        "id": "31848",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10363",
                        "value": "Isha Print",
                        "id": "10363",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10013",
                        "value": "Isha Programs and Communications",
                        "id": "10013",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19500",
                        "value": "Isha programs and Communication / Training  and Isha Programs Archives",
                        "id": "19500",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/12800",
                        "value": "Isha Programs and Communications / NP (Back Office)",
                        "id": "12800",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/20700",
                        "value": "Isha Illam",
                        "id": "20700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10366",
                        "value": "Isha Sacred Walks",
                        "id": "10366",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32399",
                        "value": "Isha Rural Health Clinic",
                        "id": "32399",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31348",
                        "value": "IT Apps",
                        "id": "31348",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31347",
                        "value": "IT Infra",
                        "id": "31347",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35988",
                        "value": "IT Infra_BLR",
                        "id": "35988",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39114",
                        "value": "Infra_BLR",
                        "id": "39114",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11101",
                        "value": "IYC Communications",
                        "id": "11101",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11100",
                        "value": "IYC Programs",
                        "id": "11100",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38759",
                        "value": "Gaushala_BLR",
                        "id": "38759",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35987",
                        "value": "Guest care",
                        "id": "35987",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38758",
                        "value": "Guest Care_BLR",
                        "id": "38758",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38831",
                        "value": "Isha Samskriti",
                        "id": "38831",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10368",
                        "value": "Isha Project Samskriti",
                        "id": "10368",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/12400",
                        "value": "IYC Communications / TN Media",
                        "id": "12400",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35995",
                        "value": "IYC Communications / Outside Tamil Nadu and Global media",
                        "id": "35995",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35989",
                        "value": "IYC Programs_BLR",
                        "id": "35989",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10401",
                        "value": "Isha Vidhya",
                        "id": "10401",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10343",
                        "value": "Kayantasthanam",
                        "id": "10343",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33897",
                        "value": "Kshetra",
                        "id": "33897",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31821",
                        "value": "Krishi Land Farm Developer",
                        "id": "31821",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31830",
                        "value": "Kshetragna Project",
                        "id": "31830",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35990",
                        "value": "KSM",
                        "id": "35990",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29975",
                        "value": "Isha Structure",
                        "id": "29975",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/25500",
                        "value": "Land Management",
                        "id": "25500",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10346",
                        "value": "Land Maintenance",
                        "id": "10346",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10344",
                        "value": "Laundry",
                        "id": "10344",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10345",
                        "value": "Legal",
                        "id": "10345",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38757",
                        "value": "Learning & Development_BLR",
                        "id": "38757",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10347",
                        "value": "Linga Bhairavi - Ashram",
                        "id": "10347",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28202",
                        "value": "Linga Bhairavi - Delhi",
                        "id": "28202",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28200",
                        "value": "Linga Bhairavi - Salem",
                        "id": "28200",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35002",
                        "value": "Livestream",
                        "id": "35002",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28201",
                        "value": "Linga Bhairavi - Gobi",
                        "id": "28201",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10348",
                        "value": "Local Welfare",
                        "id": "10348",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10350",
                        "value": "Maintenance",
                        "id": "10350",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35991",
                        "value": "Maintenance - IARF",
                        "id": "35991",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35992",
                        "value": "Maintenance - IHS",
                        "id": "35992",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10014",
                        "value": "Marketing",
                        "id": "10014",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32398",
                        "value": "Mobile Health Clinic",
                        "id": "32398",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10359",
                        "value": "Nandhi Foods",
                        "id": "10359",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33561",
                        "value": "Maintenance_BLR",
                        "id": "33561",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22202",
                        "value": "Not Applicable/Listed",
                        "id": "22202",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10353",
                        "value": "Local Welfare FBO",
                        "id": "10353",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/28100",
                        "value": "Object Promotion",
                        "id": "28100",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10532",
                        "value": "Offerings",
                        "id": "10532",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35993",
                        "value": "Offerings Back Office",
                        "id": "35993",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10351",
                        "value": "Overseas Coordination Office",
                        "id": "10351",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22002",
                        "value": "Photo Lamination",
                        "id": "22002",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35996",
                        "value": "Photo Publications_BLR",
                        "id": "35996",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/20400",
                        "value": "Plumbing",
                        "id": "20400",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10355",
                        "value": "Painting",
                        "id": "10355",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38704",
                        "value": "Parking",
                        "id": "38704",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33646",
                        "value": "Parking_BLR",
                        "id": "33646",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10362",
                        "value": "Photo Publication",
                        "id": "10362",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33567",
                        "value": "Plumbing_BLR",
                        "id": "33567",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10358",
                        "value": "Pooja Material",
                        "id": "10358",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10356",
                        "value": "Purchase",
                        "id": "10356",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33565",
                        "value": "Purchase_BLR",
                        "id": "33565",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36069",
                        "value": "Region",
                        "id": "36069",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10364",
                        "value": "Residents Coordination Department",
                        "id": "10364",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38755",
                        "value": "Residents Coordination Department_BLR",
                        "id": "38755",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31802",
                        "value": "RFR - Yavatmal",
                        "id": "31802",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33871",
                        "value": "RFR Comms",
                        "id": "33871",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31711",
                        "value": "Publishing",
                        "id": "31711",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34158",
                        "value": "Sadhanapada - Accomodation",
                        "id": "34158",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34160",
                        "value": "Sadhanapada - Applications",
                        "id": "34160",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34162",
                        "value": "Sadhanapada - Back office",
                        "id": "34162",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34164",
                        "value": "Sadhanapada - Health",
                        "id": "34164",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34163",
                        "value": "Sadhanapada - Content & Communications",
                        "id": "34163",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34159",
                        "value": "Sadhanapada - Alumni",
                        "id": "34159",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34168",
                        "value": "Sadhanapada - Promotions",
                        "id": "34168",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34161",
                        "value": "Sadhanapada - Seva",
                        "id": "34161",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34165",
                        "value": "Sadhanapada - Helpdesk",
                        "id": "34165",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34166",
                        "value": "Sadhanapada - Prana Danam",
                        "id": "34166",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10337",
                        "value": "Sadhguru Academy",
                        "id": "10337",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39334",
                        "value": "Sadhguru Academy_BLR",
                        "id": "39334",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33563",
                        "value": "Save Soil - Awareness",
                        "id": "33563",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30701",
                        "value": "Sadhanapada",
                        "id": "30701",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39330",
                        "value": "Sadhanapada_BLR",
                        "id": "39330",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34169",
                        "value": "Sadhanapada - Tech",
                        "id": "34169",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33562",
                        "value": "Save Soil - R&T",
                        "id": "33562",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33512",
                        "value": "Save Soil - TKV",
                        "id": "33512",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10367",
                        "value": "Sevadhars Quarters",
                        "id": "10367",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10369",
                        "value": "Sounds of Isha",
                        "id": "10369",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31708",
                        "value": "SGO",
                        "id": "31708",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35997",
                        "value": "Sounds of Isha_BLR",
                        "id": "35997",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33560",
                        "value": "Security_BLR",
                        "id": "33560",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10371",
                        "value": "Security",
                        "id": "10371",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10372",
                        "value": "Shivanga",
                        "id": "10372",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34167",
                        "value": "Sadhanapada - Programs",
                        "id": "34167",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/27001",
                        "value": "Spanda Hall Maintenance",
                        "id": "27001",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35998",
                        "value": "Telecom_BLR",
                        "id": "35998",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35999",
                        "value": "Transport_BLR",
                        "id": "35999",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35994",
                        "value": "SSB Offerings",
                        "id": "35994",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36225",
                        "value": "SSB Programs",
                        "id": "36225",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29300",
                        "value": "Training and Development",
                        "id": "29300",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10375",
                        "value": "Vahanam",
                        "id": "10375",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10379",
                        "value": "Volunteers Reception Office",
                        "id": "10379",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34506",
                        "value": "Velliangiri Prashala",
                        "id": "34506",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10378",
                        "value": "Volunteers Coordination Department",
                        "id": "10378",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38756",
                        "value": "Volunteers Coordination Department_BLR",
                        "id": "38756",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10376",
                        "value": "Video Publications",
                        "id": "10376",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29700",
                        "value": "Waste Management",
                        "id": "29700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10374",
                        "value": "Tamil Publications",
                        "id": "10374",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10377",
                        "value": "Vehicle Maintenance",
                        "id": "10377",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33568",
                        "value": "Waste Management_BLR",
                        "id": "33568",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36001",
                        "value": "Video Publications_BLR",
                        "id": "36001",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10380",
                        "value": "Welcome Point",
                        "id": "10380",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30705",
                        "value": "Cauvery Calling",
                        "id": "30705",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10360",
                        "value": "IAM",
                        "id": "10360",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36246",
                        "value": "Isha Samskriti / Isha Project Samskriti",
                        "id": "36246",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10012",
                        "value": "IT / IT - Applications",
                        "id": "10012",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31807",
                        "value": "CC - Central Office",
                        "id": "31807",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10357",
                        "value": "Production",
                        "id": "10357",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10308",
                        "value": "Rental Accomodaton",
                        "id": "10308",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31803",
                        "value": "RFR - Isha Agro Movement",
                        "id": "31803",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38801",
                        "value": "Samskriti",
                        "id": "38801",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22302",
                        "value": "RFR",
                        "id": "22302",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/19800",
                        "value": "Rally For Rivers",
                        "id": "19800",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36000",
                        "value": "Volunteer Coordination Department",
                        "id": "36000",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10370",
                        "value": "Spanda hall & AYA Maintenance",
                        "id": "10370",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10373",
                        "value": "Telecom",
                        "id": "10373",
                        "disabled": true
                    }
                ]
            },
            "customfield_11702": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 11702
                },
                "name": "Request Priority",
                "fieldId": "customfield_11702",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11300",
                        "value": "Urgent",
                        "id": "11300",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11301",
                        "value": "Normal",
                        "id": "11301",
                        "disabled": false
                    }
                ]
            },
            "components": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "component",
                    "system": "components"
                },
                "name": "Component/s",
                "fieldId": "components",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18200",
                        "id": "18200",
                        "name": "ashram-weekly-video"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17706",
                        "id": "17706",
                        "name": "Audio Data"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18100",
                        "id": "18100",
                        "name": "Darshan"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17500",
                        "id": "17500",
                        "name": "Edit"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/23716",
                        "id": "23716",
                        "name": "edit-approval"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18203",
                        "id": "18203",
                        "name": "fever-fm"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18000",
                        "id": "18000",
                        "name": "Footage"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/23737",
                        "id": "23737",
                        "name": "internal"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18202",
                        "id": "18202",
                        "name": "ipc-sathsang"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18201",
                        "id": "18201",
                        "name": "monthly-glimpses"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/21600",
                        "id": "21600",
                        "name": "MOV or MP4 Format"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17900",
                        "id": "17900",
                        "name": "Others"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17600",
                        "id": "17600",
                        "name": "Shoot"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17702",
                        "id": "17702",
                        "name": "TV"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/17707",
                        "id": "17707",
                        "name": "Video Data"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/component/18204",
                        "id": "18204",
                        "name": "youtube-weekly"
                    }
                ]
            },
            "customfield_63703": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:labels",
                    "customId": 63703
                },
                "name": "Content Tags",
                "fieldId": "customfield_63703",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/labels/784197/suggest?customFieldId=63703&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "labels": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "system": "labels"
                },
                "name": "Labels",
                "fieldId": "labels",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/labels/suggest?query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "reporter": {
                "required": false,
                "schema": {
                    "type": "user",
                    "system": "reporter"
                },
                "name": "Reporter",
                "fieldId": "reporter",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/latest/user/search?username=",
                "operations": [
                    "set"
                ]
            },
            "customfield_62800": {
                "required": false,
                "schema": {
                    "type": "number",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:float",
                    "customId": 62800
                },
                "name": "Video Duration ",
                "fieldId": "customfield_62800",
                "operations": [
                    "set"
                ]
            },
            "customfield_63411": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
                    "customId": 63411
                },
                "name": "Video Duration Unit",
                "fieldId": "customfield_63411",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21609",
                        "value": "Hours",
                        "id": "21609",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21610",
                        "value": "Minutes",
                        "id": "21610",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21611",
                        "value": "Seconds",
                        "id": "21611",
                        "disabled": false
                    }
                ]
            },
            "customfield_12210": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 12210
                },
                "name": "VP_Output Format",
                "fieldId": "customfield_12210",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11616",
                        "value": "MP4",
                        "id": "11616",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21502",
                        "value": "MOV",
                        "id": "21502",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34383",
                        "value": "MXF",
                        "id": "34383",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11618",
                        "value": "MP3",
                        "id": "11618",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11619",
                        "value": "WAV",
                        "id": "11619",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11617",
                        "value": "DivX",
                        "id": "11617",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11620",
                        "value": "Other",
                        "id": "11620",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11615",
                        "value": "DVD",
                        "id": "11615",
                        "disabled": true
                    }
                ]
            },
            "customfield_12205": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 12205
                },
                "name": "VP_Audience Type",
                "fieldId": "customfield_12205",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21503",
                        "value": "Social Media",
                        "id": "21503",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11600",
                        "value": "Teachers",
                        "id": "11600",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11601",
                        "value": "Residents",
                        "id": "11601",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11602",
                        "value": "Meditators",
                        "id": "11602",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11603",
                        "value": "Corporate",
                        "id": "11603",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11604",
                        "value": "Govt. Officials",
                        "id": "11604",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11605",
                        "value": "Business Leaders",
                        "id": "11605",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11606",
                        "value": "For All",
                        "id": "11606",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11607",
                        "value": "Other",
                        "id": "11607",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21504",
                        "value": "Stall",
                        "id": "21504",
                        "disabled": false
                    }
                ]
            },
            "duedate": {
                "required": false,
                "schema": {
                    "type": "date",
                    "system": "duedate"
                },
                "name": "Due Date",
                "fieldId": "duedate",
                "operations": [
                    "set"
                ]
            },
            "customfield_63704": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:labels",
                    "customId": 63704
                },
                "name": "Video Clip Tags",
                "fieldId": "customfield_63704",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/labels/784197/suggest?customFieldId=63704&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_10006": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 10006
                },
                "name": "Purpose",
                "fieldId": "customfield_10006",
                "operations": [
                    "set"
                ]
            },
            "customfield_12211": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 12211
                },
                "name": "VP_Other Language",
                "fieldId": "customfield_12211",
                "operations": [
                    "set"
                ]
            },
            "customfield_12213": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 12213
                },
                "name": "Other Platform",
                "fieldId": "customfield_12213",
                "operations": [
                    "set"
                ]
            },
            "customfield_12212": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 12212
                },
                "name": "VP_Other Audience Type",
                "fieldId": "customfield_12212",
                "operations": [
                    "set"
                ]
            },
            "customfield_12214": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 12214
                },
                "name": "VP_Other Output Format",
                "fieldId": "customfield_12214",
                "operations": [
                    "set"
                ]
            },
            "customfield_12202": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 12202
                },
                "name": "VP_Source Video Details",
                "fieldId": "customfield_12202",
                "operations": [
                    "set"
                ]
            },
            "customfield_10619": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 10619
                },
                "name": "File Name",
                "fieldId": "customfield_10619",
                "operations": [
                    "set"
                ]
            },
            "customfield_10405": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 10405
                },
                "name": "Dept Lead",
                "fieldId": "customfield_10405",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_10405&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_65304": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 65304
                },
                "name": "Project Root Folder",
                "fieldId": "customfield_65304",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22700",
                        "value": "Not Applicable",
                        "id": "22700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29600",
                        "value": " Projects/Jawagar",
                        "id": "29600",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29601",
                        "value": " Projects/Karthik.T",
                        "id": "29601",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22211",
                        "value": "Projects/Ashwin",
                        "id": "22211",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/25501",
                        "value": "Projects/Kodeeswaran",
                        "id": "25501",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22208",
                        "value": "Projects/Sathya",
                        "id": "22208",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22206",
                        "value": "Projects/Shantha",
                        "id": "22206",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22207",
                        "value": "Projects/Suren",
                        "id": "22207",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22500",
                        "value": "Projects/Sw Aloka",
                        "id": "22500",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22212",
                        "value": "Projects/Sw Bekura",
                        "id": "22212",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22205",
                        "value": "Projects/Sw Medinaja",
                        "id": "22205",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22204",
                        "value": "Projects/Sw Raksha",
                        "id": "22204",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22209",
                        "value": "Projects/Sw Vilasa",
                        "id": "22209",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22210",
                        "value": "Projects/Vinodh",
                        "id": "22210",
                        "disabled": false
                    }
                ]
            },
            "customfield_65302": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 65302
                },
                "name": "Script Path",
                "fieldId": "customfield_65302",
                "operations": [
                    "set"
                ]
            },
            "customfield_65303": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 65303
                },
                "name": "Video Languages",
                "fieldId": "customfield_65303",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22200",
                        "value": "English",
                        "id": "22200",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22201",
                        "value": "Tamil",
                        "id": "22201",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/24304",
                        "value": "Others",
                        "id": "24304",
                        "disabled": false
                    }
                ]
            },
            "customfield_10007": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 10007
                },
                "name": "Remarks",
                "fieldId": "customfield_10007",
                "operations": [
                    "set"
                ]
            },
            "customfield_65305": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 65305
                },
                "name": "Trim Backup Checklist",
                "fieldId": "customfield_65305",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22213",
                        "value": "Project File shared",
                        "id": "22213",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22214",
                        "value": "Project File Opening fine",
                        "id": "22214",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22215",
                        "value": "XML File added",
                        "id": "22215",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22216",
                        "value": "Edited Transcript Shared",
                        "id": "22216",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22217",
                        "value": "No Logo Output Added",
                        "id": "22217",
                        "disabled": false
                    }
                ]
            },
            "customfield_65307": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 65307
                },
                "name": "First Cut Done Checklist",
                "fieldId": "customfield_65307",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22223",
                        "value": "Audio Ok",
                        "id": "22223",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22224",
                        "value": "Visual Flow Ok",
                        "id": "22224",
                        "disabled": false
                    }
                ]
            },
            "customfield_65308": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 65308
                },
                "name": "Final Output Done Checklist",
                "fieldId": "customfield_65308",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22225",
                        "value": "Audio Correction Done",
                        "id": "22225",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22226",
                        "value": "Color Correction Done",
                        "id": "22226",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22227",
                        "value": "Titles & Supers Done",
                        "id": "22227",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22228",
                        "value": "CTA/End Slide Done",
                        "id": "22228",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22229",
                        "value": "Logo Added if Applicable",
                        "id": "22229",
                        "disabled": false
                    }
                ]
            },
            "customfield_65311": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 65311
                },
                "name": "CTA",
                "fieldId": "customfield_65311",
                "operations": [
                    "set"
                ]
            },
            "customfield_65502": {
                "required": false,
                "schema": {
                    "type": "datetime",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime",
                    "customId": 65502
                },
                "name": "Request Closed Date Time",
                "fieldId": "customfield_65502",
                "operations": [
                    "set"
                ]
            },
            "customfield_64101": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 64101
                },
                "name": "Video URL",
                "fieldId": "customfield_64101",
                "operations": [
                    "set"
                ]
            },
            "customfield_10001": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "user",
                    "custom": "com.atlassian.servicedesk:sd-request-participants",
                    "customId": 10001
                },
                "name": "Request participants",
                "fieldId": "customfield_10001",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/servicedesk/1/servicedesk/sd-user-search/participants?issueKey=VP-49947&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "description": {
                "required": false,
                "schema": {
                    "type": "string",
                    "system": "description"
                },
                "name": "Description",
                "fieldId": "description",
                "operations": [
                    "set"
                ]
            },
            "customfield_69703": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 69703
                },
                "name": "Project Assignee",
                "fieldId": "customfield_69703",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_69703&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_71100": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 71100
                },
                "name": "Video Code",
                "fieldId": "customfield_71100",
                "operations": [
                    "set"
                ]
            },
            "customfield_65903": {
                "required": false,
                "schema": {
                    "type": "datetime",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime",
                    "customId": 65903
                },
                "name": "Script Due Date",
                "fieldId": "customfield_65903",
                "operations": [
                    "set"
                ]
            },
            "customfield_106811": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 106811
                },
                "name": "Attachment Path",
                "fieldId": "customfield_106811",
                "operations": [
                    "set"
                ]
            },
            "customfield_106721": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 106721
                },
                "name": "VP_Key Video Messages",
                "fieldId": "customfield_106721",
                "operations": [
                    "set"
                ]
            },
            "customfield_106722": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 106722
                },
                "name": "VP_Relevant History",
                "fieldId": "customfield_106722",
                "operations": [
                    "set"
                ]
            },
            "customfield_107057": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 107057
                },
                "name": "VP_Video Complexity",
                "fieldId": "customfield_107057",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30324",
                        "value": "High",
                        "id": "30324",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30325",
                        "value": "Medium",
                        "id": "30325",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30326",
                        "value": "Low",
                        "id": "30326",
                        "disabled": false
                    }
                ]
            },
            "customfield_106744": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 106744
                },
                "name": "VP_Director",
                "fieldId": "customfield_106744",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_106744&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_12301": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 12301
                },
                "name": "Languages",
                "fieldId": "customfield_12301",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11707",
                        "value": "English",
                        "id": "11707",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11703",
                        "value": "Tamil",
                        "id": "11703",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11704",
                        "value": "Hindi",
                        "id": "11704",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11705",
                        "value": "Telugu",
                        "id": "11705",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29965",
                        "value": "Kannada",
                        "id": "29965",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11706",
                        "value": "Malayalam",
                        "id": "11706",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29967",
                        "value": "Marathi",
                        "id": "29967",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29966",
                        "value": "Gujarati",
                        "id": "29966",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30288",
                        "value": "Bengali",
                        "id": "30288",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31666",
                        "value": "Odia",
                        "id": "31666",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31667",
                        "value": "Assamese",
                        "id": "31667",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30754",
                        "value": "Nepali",
                        "id": "30754",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30330",
                        "value": "Arabic",
                        "id": "30330",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31654",
                        "value": "Bulgarian",
                        "id": "31654",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31655",
                        "value": "Czech",
                        "id": "31655",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29968",
                        "value": "French",
                        "id": "29968",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29969",
                        "value": "German",
                        "id": "29969",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31609",
                        "value": "Hebrew",
                        "id": "31609",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31656",
                        "value": "Hungarian",
                        "id": "31656",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30747",
                        "value": "Indonesian",
                        "id": "30747",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30727",
                        "value": "Italian",
                        "id": "30727",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31657",
                        "value": "Japanese",
                        "id": "31657",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31658",
                        "value": "Korean",
                        "id": "31658",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31659",
                        "value": "Mandarin Chinese",
                        "id": "31659",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11709",
                        "value": "Other",
                        "id": "11709",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31660",
                        "value": "Polish",
                        "id": "31660",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30329",
                        "value": "Portuguese",
                        "id": "30329",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37345",
                        "value": "Punjabi",
                        "id": "37345",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30728",
                        "value": "Romanian",
                        "id": "30728",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29970",
                        "value": "Russian",
                        "id": "29970",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31661",
                        "value": "Serbian",
                        "id": "31661",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29971",
                        "value": "Spanish",
                        "id": "29971",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31662",
                        "value": "Thai",
                        "id": "31662",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30729",
                        "value": "Traditional Chinese",
                        "id": "30729",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31663",
                        "value": "Turkish",
                        "id": "31663",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31664",
                        "value": "Ukranian",
                        "id": "31664",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31665",
                        "value": "Vietnamese",
                        "id": "31665",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11708",
                        "value": "Chinese",
                        "id": "11708",
                        "disabled": true
                    }
                ]
            },
            "customfield_107103": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 107103
                },
                "name": "Digital Manager",
                "fieldId": "customfield_107103",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_107103&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_107104": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 107104
                },
                "name": "VP_Digital Approval Date",
                "fieldId": "customfield_107104",
                "operations": [
                    "set"
                ]
            },
            "customfield_107520": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 107520
                },
                "name": "Thumbnail Title",
                "fieldId": "customfield_107520",
                "operations": [
                    "set"
                ]
            },
            "customfield_63425": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 63425
                },
                "name": "Event Details",
                "fieldId": "customfield_63425",
                "operations": [
                    "set"
                ]
            },
            "customfield_107609": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",
                    "customId": 107609
                },
                "name": "TP_Scripting Team",
                "fieldId": "customfield_107609",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_107609&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_107612": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 107612
                },
                "name": "VP_Dubbing Request",
                "fieldId": "customfield_107612",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30812",
                        "value": "Yes",
                        "id": "30812",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30813",
                        "value": "No",
                        "id": "30813",
                        "disabled": false
                    }
                ]
            },
            "customfield_108021": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "custom": "rs.codecentric.label-manager-project:labelManagerCustomField",
                    "customId": 108021
                },
                "name": "VP_Audience Tags",
                "fieldId": "customfield_108021",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/labelIt/1.0/items/784197/suggest?customFieldId=108021&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_108022": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "custom": "rs.codecentric.label-manager-project:labelManagerCustomField",
                    "customId": 108022
                },
                "name": "VP_Content Tags",
                "fieldId": "customfield_108022",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/labelIt/1.0/items/784197/suggest?customFieldId=108022&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_108025": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 108025
                },
                "name": "VP_Video Access",
                "fieldId": "customfield_108025",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31668",
                        "value": "Public",
                        "id": "31668",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31669",
                        "value": "Ashram Depts",
                        "id": "31669",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31670",
                        "value": "Dept Only",
                        "id": "31670",
                        "disabled": false
                    }
                ]
            },
            "customfield_108030": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:labels",
                    "customId": 108030
                },
                "name": "VP_Publisher",
                "fieldId": "customfield_108030",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/labels/784197/suggest?customFieldId=108030&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_108031": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 108031
                },
                "name": "VP_Video Duration Mins",
                "fieldId": "customfield_108031",
                "operations": [
                    "set"
                ]
            },
            "customfield_108124": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 108124
                },
                "name": "VP_SG Edit Req",
                "fieldId": "customfield_108124",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31767",
                        "value": "Yes",
                        "id": "31767",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31768",
                        "value": "No",
                        "id": "31768",
                        "disabled": false
                    }
                ]
            },
            "customfield_108123": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 108123
                },
                "name": "VP_SG Shoot Type",
                "fieldId": "customfield_108123",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31764",
                        "value": "Candid",
                        "id": "31764",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31765",
                        "value": "DMQ",
                        "id": "31765",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33495",
                        "value": "BR Meet",
                        "id": "33495",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31766",
                        "value": "Others",
                        "id": "31766",
                        "disabled": false
                    }
                ]
            },
            "customfield_69300": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 69300
                },
                "name": "LTO Request Folder Name",
                "fieldId": "customfield_69300",
                "operations": [
                    "set"
                ]
            },
            "timetracking": {
                "required": false,
                "schema": {
                    "type": "timetracking",
                    "system": "timetracking"
                },
                "name": "Time Tracking",
                "fieldId": "timetracking",
                "operations": [
                    "set",
                    "edit"
                ]
            },
            "customfield_109419": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109419
                },
                "name": "VP_Topical",
                "fieldId": "customfield_109419",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33663",
                        "value": "Yes",
                        "id": "33663",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33664",
                        "value": "No",
                        "id": "33664",
                        "disabled": false
                    }
                ]
            },
            "customfield_109420": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109420
                },
                "name": "VP_Month",
                "fieldId": "customfield_109420",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33665",
                        "value": "2023-Jan",
                        "id": "33665",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33666",
                        "value": "2023-Feb",
                        "id": "33666",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33667",
                        "value": "2023-Mar",
                        "id": "33667",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33668",
                        "value": "2023-Apr",
                        "id": "33668",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33669",
                        "value": "2023-May",
                        "id": "33669",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33670",
                        "value": "2023-Jun",
                        "id": "33670",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33671",
                        "value": "2023-Jul",
                        "id": "33671",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33672",
                        "value": "2023-Aug",
                        "id": "33672",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33673",
                        "value": "2023-Sep",
                        "id": "33673",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33674",
                        "value": "2023-Oct",
                        "id": "33674",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33675",
                        "value": "2023-Nov",
                        "id": "33675",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33676",
                        "value": "2023-Dec",
                        "id": "33676",
                        "disabled": false
                    }
                ]
            },
            "customfield_109421": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109421
                },
                "name": "Team",
                "fieldId": "customfield_109421",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36112",
                        "value": "End User Support Team",
                        "id": "36112",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33880",
                        "value": "VP DMQ-Candid",
                        "id": "33880",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33887",
                        "value": "VP Shoots CC RFR SS",
                        "id": "33887",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33888",
                        "value": "VP CC SS Edits",
                        "id": "33888",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33890",
                        "value": "VP SGYT Tamil LAU Reels",
                        "id": "33890",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33895",
                        "value": "Vpub SG EX",
                        "id": "33895",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34134",
                        "value": "VP Editors ",
                        "id": "34134",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34199",
                        "value": "VP IF Eng LAU",
                        "id": "34199",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34323",
                        "value": "VP SG LAU",
                        "id": "34323",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34449",
                        "value": "CC KA",
                        "id": "34449",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34488",
                        "value": "Photo Team",
                        "id": "34488",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34999",
                        "value": "VP Sadhguru Brands",
                        "id": "34999",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35419",
                        "value": "vp campaigns",
                        "id": "35419",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35420",
                        "value": "Impressions",
                        "id": "35420",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35700",
                        "value": "Transcription",
                        "id": "35700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33802",
                        "value": "VP Shoots Regular",
                        "id": "33802",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36240",
                        "value": "Sg Reach",
                        "id": "36240",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37014",
                        "value": "Tamil Transcription Team",
                        "id": "37014",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37107",
                        "value": "Illustrators",
                        "id": "37107",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37133",
                        "value": "Electrical Store",
                        "id": "37133",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37142",
                        "value": "Tr Team Eng+Tam",
                        "id": "37142",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37706",
                        "value": "Transcription Edited Team",
                        "id": "37706",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37716",
                        "value": "Transcription Edited Team (Satya)",
                        "id": "37716",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37717",
                        "value": "Transcription Raw Team",
                        "id": "37717",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37961",
                        "value": "English Edited Transcription Team(Rohit)",
                        "id": "37961",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37962",
                        "value": "English Transcription Raw Team (Rohit)",
                        "id": "37962",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37963",
                        "value": "Tamil Transcription Team (Rohit)",
                        "id": "37963",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38030",
                        "value": "video pub",
                        "id": "38030",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38100",
                        "value": "Tamil Transcription Team(Satya)",
                        "id": "38100",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39070",
                        "value": "Nithya monitoring",
                        "id": "39070",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33692",
                        "value": "IMP-Team 5",
                        "id": "33692",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33678",
                        "value": "Bangalore",
                        "id": "33678",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33679",
                        "value": "DEVOPS",
                        "id": "33679",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33680",
                        "value": "Devops Team",
                        "id": "33680",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33681",
                        "value": "Electrical AC /WR",
                        "id": "33681",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33682",
                        "value": "Electrical Backoffice",
                        "id": "33682",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33683",
                        "value": "Electrical Maintenance & Program",
                        "id": "33683",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33684",
                        "value": "Electrical Preventive Maintenance",
                        "id": "33684",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33685",
                        "value": "Electrical Project",
                        "id": "33685",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33686",
                        "value": "Electrical QRT",
                        "id": "33686",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33687",
                        "value": "Electrical Work Request & AC",
                        "id": "33687",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33688",
                        "value": "IMP-Team 1",
                        "id": "33688",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33689",
                        "value": "IMP-Team 2 - Ca Ca",
                        "id": "33689",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33690",
                        "value": "IMP-Team 3",
                        "id": "33690",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33691",
                        "value": "IMP-Team 4",
                        "id": "33691",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33677",
                        "value": "Ashram",
                        "id": "33677",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33693",
                        "value": "IMP-Team 6",
                        "id": "33693",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33694",
                        "value": "IMP-Team 7",
                        "id": "33694",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33695",
                        "value": "IMP-Team 8 Illust.",
                        "id": "33695",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33697",
                        "value": "VP Sadhguru Exclusive",
                        "id": "33697",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33698",
                        "value": "[EMSD] Emedia Service Desk Team",
                        "id": "33698",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33699",
                        "value": "[EPSD] English Publications Service Desk Team",
                        "id": "33699",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33700",
                        "value": "[GLP] Global Languages Service Desk Team",
                        "id": "33700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33701",
                        "value": "[IE] Isha Events Team",
                        "id": "33701",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33702",
                        "value": "[ILPSD] ILP Service Desk Team",
                        "id": "33702",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33703",
                        "value": "[ISD] Impressions Service Desk Team",
                        "id": "33703",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33704",
                        "value": "[JS] Jira support  Team",
                        "id": "33704",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33705",
                        "value": "tempo-teams.upgrade.default.team.name",
                        "id": "33705",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33706",
                        "value": "VP SGYT Eng LAU",
                        "id": "33706",
                        "disabled": false
                    }
                ]
            },
            "customfield_109439": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 109439
                },
                "name": "VP_TrimBackupClosedDate",
                "fieldId": "customfield_109439",
                "operations": [
                    "set"
                ]
            },
            "customfield_108134": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 108134
                },
                "name": "IE_Trim Backup Done Date",
                "fieldId": "customfield_108134",
                "operations": [
                    "set"
                ]
            },
            "customfield_109206": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 109206
                },
                "name": "Project Manager",
                "fieldId": "customfield_109206",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_109206&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_106757": {
                "required": false,
                "schema": {
                    "type": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
                    "customId": 106757
                },
                "name": "Script Writer",
                "fieldId": "customfield_106757",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_106757&query=",
                "operations": [
                    "set"
                ]
            },
            "customfield_109523": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 109523
                },
                "name": "VP_TrimBackupDueDate",
                "fieldId": "customfield_109523",
                "operations": [
                    "set"
                ]
            },
            "customfield_21401": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",
                    "customId": 21401
                },
                "name": "Approvers",
                "fieldId": "customfield_21401",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_21401&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_102201": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 102201
                },
                "name": "Delivered Date",
                "fieldId": "customfield_102201",
                "operations": [
                    "set"
                ]
            },
            "customfield_109647": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 109647
                },
                "name": "Posting Date",
                "fieldId": "customfield_109647",
                "operations": [
                    "set"
                ]
            },
            "customfield_109648": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109648
                },
                "name": "Planning Type",
                "fieldId": "customfield_109648",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34431",
                        "value": "Planned",
                        "id": "34431",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34432",
                        "value": "Adhoc",
                        "id": "34432",
                        "disabled": false
                    }
                ]
            },
            "customfield_107626": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",
                    "customId": 107626
                },
                "name": "Event Team",
                "fieldId": "customfield_107626",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_107626&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_109552": {
                "required": false,
                "schema": {
                    "type": "any",
                    "custom": "com.fca.jira.plugins.workflowToolbox.workflow-toolbox:calculated-number-field",
                    "customId": 109552
                },
                "name": "Delay_Days",
                "fieldId": "customfield_109552",
                "operations": [
                    "set"
                ]
            },
            "customfield_109650": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 109650
                },
                "name": "Insta Link",
                "fieldId": "customfield_109650",
                "operations": [
                    "set"
                ]
            },
            "customfield_109649": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 109649
                },
                "name": "YouTube Link",
                "fieldId": "customfield_109649",
                "operations": [
                    "set"
                ]
            },
            "customfield_109651": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 109651
                },
                "name": "Facebook Link",
                "fieldId": "customfield_109651",
                "operations": [
                    "set"
                ]
            },
            "customfield_109652": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:url",
                    "customId": 109652
                },
                "name": "Other Link",
                "fieldId": "customfield_109652",
                "operations": [
                    "set"
                ]
            },
            "customfield_107625": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 107625
                },
                "name": "Urgent Reason",
                "fieldId": "customfield_107625",
                "operations": [
                    "set"
                ]
            },
            "customfield_109700": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109700
                },
                "name": "Approval Video",
                "fieldId": "customfield_109700",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34600",
                        "value": "Yes",
                        "id": "34600",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34601",
                        "value": "No",
                        "id": "34601",
                        "disabled": false
                    }
                ]
            },
            "customfield_64701": {
                "required": false,
                "schema": {
                    "type": "datetime",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime",
                    "customId": 64701
                },
                "name": "Project Start Date",
                "fieldId": "customfield_64701",
                "operations": [
                    "set"
                ]
            },
            "customfield_63202": {
                "required": false,
                "schema": {
                    "type": "datetime",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datetime",
                    "customId": 63202
                },
                "name": "Project End Date",
                "fieldId": "customfield_63202",
                "operations": [
                    "set"
                ]
            },
            "customfield_109902": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 109902
                },
                "name": "Subtitles Needed",
                "fieldId": "customfield_109902",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35307",
                        "value": "Yes",
                        "id": "35307",
                        "disabled": false
                    }
                ]
            },
            "customfield_109869": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109869
                },
                "name": "Event Coverage",
                "fieldId": "customfield_109869",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35032",
                        "value": "Yes",
                        "id": "35032",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35033",
                        "value": "No",
                        "id": "35033",
                        "disabled": false
                    }
                ]
            },
            "customfield_109642": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109642
                },
                "name": "Vertical",
                "fieldId": "customfield_109642",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34404",
                        "value": "Sadhguru Reach",
                        "id": "34404",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34405",
                        "value": "Sadhguru Brands",
                        "id": "34405",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34407",
                        "value": "Campaign",
                        "id": "34407",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34451",
                        "value": "Products",
                        "id": "34451",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34406",
                        "value": "Other Depts",
                        "id": "34406",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34700",
                        "value": "CC SS",
                        "id": "34700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37705",
                        "value": "MOM APP",
                        "id": "37705",
                        "disabled": false
                    }
                ]
            },
            "customfield_109417": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109417
                },
                "name": "VP_Content Bucket",
                "fieldId": "customfield_109417",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36238",
                        "value": "Long",
                        "id": "36238",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36239",
                        "value": "Reel",
                        "id": "36239",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33662",
                        "value": "SG App",
                        "id": "33662",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33652",
                        "value": "SG Exclusive",
                        "id": "33652",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33654",
                        "value": "SG DMQ",
                        "id": "33654",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33884",
                        "value": "SG Candid Reel",
                        "id": "33884",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33656",
                        "value": "Campaign",
                        "id": "33656",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33879",
                        "value": "RFR-CC-SS",
                        "id": "33879",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33883",
                        "value": "Other Depts",
                        "id": "33883",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36070",
                        "value": "Approval Only",
                        "id": "36070",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35078",
                        "value": "Campaign Promo Reel",
                        "id": "35078",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35074",
                        "value": "Campaign Wisdom Reel - SG",
                        "id": "35074",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35319",
                        "value": "Campaign Reel - SG Brands",
                        "id": "35319",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33653",
                        "value": "SG Eng Long",
                        "id": "33653",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33655",
                        "value": "SG Eng Reel",
                        "id": "33655",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34453",
                        "value": "IF Long",
                        "id": "34453",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33658",
                        "value": "IF Reel",
                        "id": "33658",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34460",
                        "value": "CP Long",
                        "id": "34460",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34461",
                        "value": "CP Reel",
                        "id": "34461",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34454",
                        "value": "DL Long",
                        "id": "34454",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34455",
                        "value": "DL Reel",
                        "id": "34455",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34456",
                        "value": "AY Long",
                        "id": "34456",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34457",
                        "value": "AY Reel",
                        "id": "34457",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34458",
                        "value": "LB Long",
                        "id": "34458",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34459",
                        "value": "LB Reel",
                        "id": "34459",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33657",
                        "value": "SG Tamil Long",
                        "id": "33657",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34452",
                        "value": "SG Tamil Reel",
                        "id": "34452",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35840",
                        "value": "SSBD - SG APP",
                        "id": "35840",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33882",
                        "value": "IPC",
                        "id": "33882",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33660",
                        "value": "Adhoc Eng",
                        "id": "33660",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33659",
                        "value": "Adhoc Tamil",
                        "id": "33659",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33661",
                        "value": "Adhoc IF",
                        "id": "33661",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33881",
                        "value": "Unassigned",
                        "id": "33881",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/33885",
                        "value": "SGO Adhoc",
                        "id": "33885",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35079",
                        "value": "Event Coverage",
                        "id": "35079",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35824",
                        "value": "Glimpses",
                        "id": "35824",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38750",
                        "value": "MOM Reel",
                        "id": "38750",
                        "disabled": true
                    }
                ]
            },
            "customfield_106754": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 106754
                },
                "name": "Event or Campaign",
                "fieldId": "customfield_106754",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36700",
                        "value": "Ananda Alai",
                        "id": "36700",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32070",
                        "value": "Annadanam-BIK",
                        "id": "32070",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38300",
                        "value": "AY-SM",
                        "id": "38300",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38206",
                        "value": "Bhairavi Sadhana",
                        "id": "38206",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35839",
                        "value": "Brand Insight",
                        "id": "35839",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29959",
                        "value": "Cauvery Calling-CAC",
                        "id": "29959",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32093",
                        "value": "Conscious Planet-CPL",
                        "id": "32093",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35704",
                        "value": "COP 28",
                        "id": "35704",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38303",
                        "value": "CP-SM",
                        "id": "38303",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39110",
                        "value": "Deeksha Program",
                        "id": "39110",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39146",
                        "value": "Devi Abhishekam",
                        "id": "39146",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39111",
                        "value": "Dhyanalinga Consecration Day - Livestream",
                        "id": "39111",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38204",
                        "value": "Diwali",
                        "id": "38204",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38304",
                        "value": "DL-SM",
                        "id": "38304",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36230",
                        "value": "Ecstasy of Enlightenment",
                        "id": "36230",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35007",
                        "value": "EU Tour Oct 2023",
                        "id": "35007",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35315",
                        "value": "FamEx",
                        "id": "35315",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32084",
                        "value": "Forest Flower-DFF",
                        "id": "32084",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32067",
                        "value": "Full Moon Flirtations-FMF",
                        "id": "32067",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34387",
                        "value": "G20",
                        "id": "34387",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32069",
                        "value": "Grace of Yoga-GOY",
                        "id": "32069",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34465",
                        "value": "Gramotsavam",
                        "id": "34465",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39112",
                        "value": "Guru Purnima at Linga Bhairavi",
                        "id": "39112",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32089",
                        "value": "Guru Purnima-GUR",
                        "id": "32089",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39107",
                        "value": "Guruvin Madiyil",
                        "id": "39107",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32066",
                        "value": "Hata Yoga Teacher Training-HYS",
                        "id": "32066",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32083",
                        "value": "HINAR-HNR",
                        "id": "32083",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29961",
                        "value": "IEC Chennai",
                        "id": "29961",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29960",
                        "value": "IEC Delhi",
                        "id": "29960",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32088",
                        "value": "IF LAU",
                        "id": "32088",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38302",
                        "value": "IF-SM",
                        "id": "38302",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32068",
                        "value": "Inner Engineering-IEP",
                        "id": "32068",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32078",
                        "value": "Insight-INS",
                        "id": "32078",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32090",
                        "value": "International Day of Yoga-IDY",
                        "id": "32090",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35421",
                        "value": "Isha Health Solutions",
                        "id": "35421",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32091",
                        "value": "Isha Outreach-OUT",
                        "id": "32091",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32076",
                        "value": "Ishanga 7%-BIK",
                        "id": "32076",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37170",
                        "value": "IYP Tamil Program",
                        "id": "37170",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/40217",
                        "value": "Kailash - Ride With Sadhguru",
                        "id": "40217",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38205",
                        "value": "Karthigai Deepam",
                        "id": "38205",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38029",
                        "value": "LAU",
                        "id": "38029",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38301",
                        "value": "LB-SM",
                        "id": "38301",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38703",
                        "value": "Lunar Hindu New Year",
                        "id": "38703",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32074",
                        "value": "Mahalaya Amavasya-LBS",
                        "id": "32074",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32082",
                        "value": "Mahashivratri-MSR",
                        "id": "32082",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38219",
                        "value": "Margazhi",
                        "id": "38219",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35075",
                        "value": "Meet Mingle Meditate - Dubai",
                        "id": "35075",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35076",
                        "value": "Meet Mingle Meditate - London",
                        "id": "35076",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34604",
                        "value": "Meet Mingle Meditate - Milan",
                        "id": "34604",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37720",
                        "value": "Miracle Of Mind",
                        "id": "37720",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34430",
                        "value": "Naga Panchami at SSB",
                        "id": "34430",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32075",
                        "value": "Navratri-NAV",
                        "id": "32075",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34388",
                        "value": "New IEO",
                        "id": "34388",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29962",
                        "value": "Not Applicable",
                        "id": "29962",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35318",
                        "value": "Not listed here",
                        "id": "35318",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32092",
                        "value": "Other-OTR",
                        "id": "32092",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34464",
                        "value": "Pancha Bhuta Kriya",
                        "id": "34464",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34701",
                        "value": "Poornanga",
                        "id": "34701",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38211",
                        "value": "Positive Stories",
                        "id": "38211",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37167",
                        "value": "Pranadanam",
                        "id": "37167",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32072",
                        "value": "Project Samskriti-PSM",
                        "id": "32072",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32071",
                        "value": "Rudraksh Diksha-RUD",
                        "id": "32071",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32065",
                        "value": "Sadhanapada-SPD",
                        "id": "32065",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32079",
                        "value": "Sadhguru App-APP",
                        "id": "32079",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32085",
                        "value": "Sadhguru Books",
                        "id": "32085",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34603",
                        "value": "Sadhguru Enlightenment Day",
                        "id": "34603",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32086",
                        "value": "Sadhguru Events",
                        "id": "32086",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32064",
                        "value": "Sadhguru Exclusive-SGX",
                        "id": "32064",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35030",
                        "value": "Sadhguru in Australia",
                        "id": "35030",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34466",
                        "value": "Sadhguru Podcasts",
                        "id": "34466",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34463",
                        "value": "Sadhguru Sannidhi Bengaluru",
                        "id": "34463",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34702",
                        "value": "Sadhguru Sannidhi Sangha",
                        "id": "34702",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37168",
                        "value": "Sahabagi",
                        "id": "37168",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32080",
                        "value": "Sanghamitra",
                        "id": "32080",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35702",
                        "value": "Saptarishi Aarti - Ashram",
                        "id": "35702",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35701",
                        "value": "Saptarishi Avahanam - SSB",
                        "id": "35701",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32073",
                        "value": "Save the Weave-STV",
                        "id": "32073",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35031",
                        "value": "SCCP",
                        "id": "35031",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35703",
                        "value": "Science and Spirituality",
                        "id": "35703",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32087",
                        "value": "SG LAU",
                        "id": "32087",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36702",
                        "value": "SGDA",
                        "id": "36702",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36531",
                        "value": "Shiva Arpane",
                        "id": "36531",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32077",
                        "value": "Shivanga-SHV",
                        "id": "32077",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34703",
                        "value": "SOI",
                        "id": "34703",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36627",
                        "value": "SSB - Kshetra Sankalpa",
                        "id": "36627",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35413",
                        "value": "SSB - Trishul & Nandi",
                        "id": "35413",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36501",
                        "value": "Tamil Thembu",
                        "id": "36501",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38220",
                        "value": "Temple Offerings",
                        "id": "38220",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32081",
                        "value": "Thaipusam",
                        "id": "32081",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37169",
                        "value": "UyirNokkam",
                        "id": "37169",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34462",
                        "value": "Yantra Ceremony",
                        "id": "34462",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37166",
                        "value": "Yoga Sandhya",
                        "id": "37166",
                        "disabled": false
                    }
                ]
            },
            "customfield_110000": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 110000
                },
                "name": "Campaign Video Type",
                "fieldId": "customfield_110000",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35414",
                        "value": "Wisdom Reel - SG",
                        "id": "35414",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35415",
                        "value": "Reel - SG Brands",
                        "id": "35415",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39136",
                        "value": "Long Form Video",
                        "id": "39136",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35416",
                        "value": "Promo Reel",
                        "id": "35416",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35417",
                        "value": "Paid",
                        "id": "35417",
                        "disabled": false
                    }
                ]
            },
            "customfield_109909": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 109909
                },
                "name": "Paid Campaign Asset",
                "fieldId": "customfield_109909",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35316",
                        "value": "Yes",
                        "id": "35316",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35317",
                        "value": "No",
                        "id": "35317",
                        "disabled": false
                    }
                ]
            },
            "customfield_110006": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 110006
                },
                "name": "Asset Plan",
                "fieldId": "customfield_110006",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35418",
                        "value": "Yes",
                        "id": "35418",
                        "disabled": false
                    }
                ]
            },
            "customfield_107037": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 107037
                },
                "name": "IM_Project discussed",
                "fieldId": "customfield_107037",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30314",
                        "value": "Yes",
                        "id": "30314",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/30315",
                        "value": "No",
                        "id": "30315",
                        "disabled": false
                    }
                ]
            },
            "customfield_107629": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 107629
                },
                "name": "Imp_DiscussedWith",
                "fieldId": "customfield_107629",
                "operations": [
                    "set"
                ]
            },
            "customfield_109903": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109903
                },
                "name": "GCP Approval",
                "fieldId": "customfield_109903",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35308",
                        "value": "Not Applicable",
                        "id": "35308",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35309",
                        "value": "Approved",
                        "id": "35309",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35310",
                        "value": "Pending",
                        "id": "35310",
                        "disabled": false
                    }
                ]
            },
            "customfield_108529": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 108529
                },
                "name": "Social Media Account",
                "fieldId": "customfield_108529",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32372",
                        "value": "Isha Foundation",
                        "id": "32372",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32371",
                        "value": "Sadhguru",
                        "id": "32371",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36065",
                        "value": "Regional Social Media Accounts",
                        "id": "36065",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34154",
                        "value": "Conscious Planet",
                        "id": "34154",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34153",
                        "value": "Dhyanalinga",
                        "id": "34153",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32373",
                        "value": "Linga Bhairavi",
                        "id": "32373",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34152",
                        "value": "Adiyogi",
                        "id": "34152",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34382",
                        "value": "Isha Life",
                        "id": "34382",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35838",
                        "value": "Sadhguru Academy",
                        "id": "35838",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/38702",
                        "value": "Miracle of Mind",
                        "id": "38702",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32379",
                        "value": "Others",
                        "id": "32379",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32375",
                        "value": "HYS",
                        "id": "32375",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32378",
                        "value": "IBPL",
                        "id": "32378",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32376",
                        "value": "Sacred Walks",
                        "id": "32376",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32374",
                        "value": "Sadhanapada",
                        "id": "32374",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32380",
                        "value": "Samskriti",
                        "id": "32380",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32377",
                        "value": "Shivanga",
                        "id": "32377",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34155",
                        "value": "Rally for Rivers",
                        "id": "34155",
                        "disabled": true
                    }
                ]
            },
            "customfield_12207": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiselect",
                    "customId": 12207
                },
                "name": "Social Media Platform",
                "fieldId": "customfield_12207",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11611",
                        "value": "YouTube",
                        "id": "11611",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21506",
                        "value": "Instagram",
                        "id": "21506",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21505",
                        "value": "Twitter",
                        "id": "21505",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11612",
                        "value": "Facebook",
                        "id": "11612",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11613",
                        "value": "WhatsApp",
                        "id": "11613",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31875",
                        "value": "Sadhguru Exclusive",
                        "id": "31875",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34756",
                        "value": "Sadhguru App",
                        "id": "34756",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11610",
                        "value": "TV Channels",
                        "id": "11610",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35812",
                        "value": "Regional Social Media Accounts",
                        "id": "35812",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34151",
                        "value": "YouTube Shorts",
                        "id": "34151",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11608",
                        "value": "Ashram Programs",
                        "id": "11608",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11609",
                        "value": "Outside Programs",
                        "id": "11609",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22203",
                        "value": "Class",
                        "id": "22203",
                        "disabled": true
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37174",
                        "value": "LinkedIn",
                        "id": "37174",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/37173",
                        "value": "X",
                        "id": "37173",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11614",
                        "value": "Other",
                        "id": "11614",
                        "disabled": false
                    }
                ]
            },
            "customfield_110335": {
                "required": false,
                "schema": {
                    "type": "date",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
                    "customId": 110335
                },
                "name": "Requested Posting Date",
                "fieldId": "customfield_110335",
                "operations": [
                    "set"
                ]
            },
            "customfield_110336": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 110336
                },
                "name": "Suggested Title",
                "fieldId": "customfield_110336",
                "operations": [
                    "set"
                ]
            },
            "customfield_109335": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 109335
                },
                "name": "VP_Trimbackup Folder Name",
                "fieldId": "customfield_109335",
                "operations": [
                    "set"
                ]
            },
            "customfield_110355": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 110355
                },
                "name": "Year",
                "fieldId": "customfield_110355",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36137",
                        "value": "2023",
                        "id": "36137",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36138",
                        "value": "2024",
                        "id": "36138",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36139",
                        "value": "2025",
                        "id": "36139",
                        "disabled": false
                    }
                ]
            },
            "customfield_110356": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 110356
                },
                "name": "Month",
                "fieldId": "customfield_110356",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36140",
                        "value": "TBD",
                        "id": "36140",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36141",
                        "value": "Jan",
                        "id": "36141",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36142",
                        "value": "Feb",
                        "id": "36142",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36143",
                        "value": "Mar",
                        "id": "36143",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36144",
                        "value": "Apr",
                        "id": "36144",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36145",
                        "value": "May",
                        "id": "36145",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36146",
                        "value": "Jun",
                        "id": "36146",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36147",
                        "value": "Jul",
                        "id": "36147",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36148",
                        "value": "Aug",
                        "id": "36148",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36149",
                        "value": "Sep",
                        "id": "36149",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36150",
                        "value": "Oct",
                        "id": "36150",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36151",
                        "value": "Nov",
                        "id": "36151",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36152",
                        "value": "Dec",
                        "id": "36152",
                        "disabled": false
                    }
                ]
            },
            "customfield_53800": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 53800
                },
                "name": "Contact Number",
                "fieldId": "customfield_53800",
                "operations": [
                    "set"
                ]
            },
            "customfield_110508": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "user",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",
                    "customId": 110508
                },
                "name": "Late Ticket Approvers",
                "fieldId": "customfield_110508",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/1.0/users/picker?fieldName=customfield_110508&query=",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ]
            },
            "customfield_65301": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 65301
                },
                "name": "Project Folder Name",
                "fieldId": "customfield_65301",
                "operations": [
                    "set"
                ]
            },
            "customfield_62801": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 62801
                },
                "name": "Output File Path",
                "fieldId": "customfield_62801",
                "operations": [
                    "set"
                ]
            },
            "customfield_63203": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
                    "customId": 63203
                },
                "name": "Backup Type",
                "fieldId": "customfield_63203",
                "operations": [
                    "add",
                    "set",
                    "remove"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21501",
                        "value": "Stems",
                        "id": "21501",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21500",
                        "value": "Consolidated",
                        "id": "21500",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/31842",
                        "value": "Others",
                        "id": "31842",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/23500",
                        "value": "Not Applicable",
                        "id": "23500",
                        "disabled": false
                    }
                ]
            },
            "customfield_109859": {
                "required": false,
                "schema": {
                    "type": "option",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
                    "customId": 109859
                },
                "name": "Lang stems needed immediately",
                "fieldId": "customfield_109859",
                "operations": [
                    "set"
                ],
                "allowedValues": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35000",
                        "value": "Yes",
                        "id": "35000",
                        "disabled": false
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35001",
                        "value": "No",
                        "id": "35001",
                        "disabled": false
                    }
                ]
            },
            "customfield_109501": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
                    "customId": 109501
                },
                "name": "Stems Location",
                "fieldId": "customfield_109501",
                "operations": [
                    "set"
                ]
            },
            "customfield_110396": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 110396
                },
                "name": "Stems Remarks",
                "fieldId": "customfield_110396",
                "operations": [
                    "set"
                ]
            },
            "customfield_108114": {
                "required": false,
                "schema": {
                    "type": "string",
                    "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textarea",
                    "customId": 108114
                },
                "name": "IE_Video File Names",
                "fieldId": "customfield_108114",
                "operations": [
                    "set"
                ]
            },
            "customfield_111600": {
                "required": false,
                "schema": {
                    "type": "array",
                    "items": "group",
                    "custom": "com.atlassian.servicedesk:sd-request-groups",
                    "customId": 111600
                },
                "name": "Groups",
                "fieldId": "customfield_111600",
                "operations": [
                    "set"
                ]
            },
            "assignee": {
                "required": false,
                "schema": {
                    "type": "user",
                    "system": "assignee"
                },
                "name": "Assignee",
                "fieldId": "assignee",
                "autoCompleteUrl": "https://servicedesk.isha.in/rest/api/latest/user/assignable/search?issueKey=VP-49947&username=",
                "operations": [
                    "set"
                ]
            },
            "comment": {
                "required": false,
                "schema": {
                    "type": "comments-page",
                    "system": "comment"
                },
                "name": "Comment",
                "fieldId": "comment",
                "operations": [
                    "add",
                    "edit",
                    "remove"
                ]
            }
        }
    },
    "changelog": {
        "startAt": 0,
        "maxResults": 16,
        "total": 16,
        "histories": [
            {
                "id": "9076755",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=lakshmi.cr",
                    "name": "lakshmi.cr",
                    "key": "lakshmi.cr",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=lakshmi.cr&avatarId=20886",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=lakshmi.cr&avatarId=20886",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=lakshmi.cr&avatarId=20886",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=lakshmi.cr&avatarId=20886"
                    },
                    "displayName": "lakshmi cr.",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-01T12:36:36.000+0530",
                "items": [
                    {
                        "field": "resolution",
                        "fieldtype": "jira",
                        "from": null,
                        "fromString": null,
                        "to": "10600",
                        "toString": "Pending Approval"
                    }
                ]
            },
            {
                "id": "9076757",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=lakshmi.cr",
                    "name": "lakshmi.cr",
                    "key": "lakshmi.cr",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=lakshmi.cr&avatarId=20886",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=lakshmi.cr&avatarId=20886",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=lakshmi.cr&avatarId=20886",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=lakshmi.cr&avatarId=20886"
                    },
                    "displayName": "lakshmi cr.",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-01T12:36:36.000+0530",
                "items": [
                    {
                        "field": "Link",
                        "fieldtype": "jira",
                        "from": null,
                        "fromString": null,
                        "to": "OCD-20113",
                        "toString": "This issue relates to OCD-20113"
                    }
                ]
            },
            {
                "id": "9106466",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-08T16:00:21.000+0530",
                "items": [
                    {
                        "field": "Trim Backup Checklist",
                        "fieldtype": "custom",
                        "from": null,
                        "fromString": null,
                        "to": "[22213, 22214, 22215, 22216, 22217]",
                        "toString": "Project File shared,Project File Opening fine,XML File added,Edited Transcript Shared,No Logo Output Added"
                    },
                    {
                        "field": "VP_Trimbackup Folder Name",
                        "fieldtype": "custom",
                        "from": null,
                        "fromString": null,
                        "to": null,
                        "toString": "Z:\\_trimbackup\\Koushik anand\\OCD-20113_How_To_Experience_Real_Sadhguru"
                    },
                    {
                        "field": "duedate",
                        "fieldtype": "jira",
                        "from": "2025-09-04",
                        "fromString": "2025-09-04 00:00:00.0",
                        "to": "2025-10-11",
                        "toString": "2025-10-11 16:00:21.304"
                    }
                ]
            },
            {
                "id": "9106467",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-08T16:00:21.000+0530",
                "items": [
                    {
                        "field": "IE_Trim Backup Done Date",
                        "fieldtype": "custom",
                        "from": null,
                        "fromString": null,
                        "to": "2025-10-08",
                        "toString": "8/Oct/25"
                    }
                ]
            },
            {
                "id": "9106468",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-08T16:00:21.000+0530",
                "items": [
                    {
                        "field": "VP_TrimBackupDoneDate",
                        "fieldtype": "custom",
                        "from": null,
                        "fromString": null,
                        "to": "2025-10-08",
                        "toString": "8/Oct/25"
                    }
                ]
            },
            {
                "id": "9106469",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=koushik.anand",
                    "name": "koushik.anand",
                    "key": "JIRAUSER97254",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=48",
                        "24x24": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=24",
                        "16x16": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=16",
                        "32x32": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=32"
                    },
                    "displayName": "Koushik Anand",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-08T16:00:21.000+0530",
                "items": [
                    {
                        "field": "assignee",
                        "fieldtype": "jira",
                        "from": "JIRAUSER97254",
                        "fromString": "Koushik Anand",
                        "to": "JIRAUSER89217",
                        "toString": "venugopalreddy Gajjela"
                    },
                    {
                        "field": "status",
                        "fieldtype": "jira",
                        "from": "14800",
                        "fromString": "Open",
                        "to": "10901",
                        "toString": "Trim Backup Done"
                    }
                ]
            },
            {
                "id": "9115863",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                    "name": "archives.bot",
                    "key": "JIRAUSER97240",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                    },
                    "displayName": "Archives Bot",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:05:22.000+0530",
                "items": [
                    {
                        "field": "status",
                        "fieldtype": "jira",
                        "from": "10901",
                        "fromString": "Trim Backup Done",
                        "to": "4",
                        "toString": "Reopened"
                    }
                ]
            },
            {
                "id": "9115900",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:12:35.000+0530",
                "items": [
                    {
                        "field": "duedate",
                        "fieldtype": "jira",
                        "from": "2025-10-11",
                        "fromString": "2025-10-11 16:00:21.0",
                        "to": "2025-10-12",
                        "toString": "2025-10-12 19:12:35.18"
                    }
                ]
            },
            {
                "id": "9115901",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:12:35.000+0530",
                "items": [
                    {
                        "field": "IE_Trim Backup Done Date",
                        "fieldtype": "custom",
                        "from": "2025-10-08",
                        "fromString": "8/Oct/25",
                        "to": "2025-10-09",
                        "toString": "9/Oct/25"
                    }
                ]
            },
            {
                "id": "9115902",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:12:35.000+0530",
                "items": [
                    {
                        "field": "VP_TrimBackupDoneDate",
                        "fieldtype": "custom",
                        "from": "2025-10-08",
                        "fromString": "8/Oct/25",
                        "to": "2025-10-09",
                        "toString": "9/Oct/25"
                    }
                ]
            },
            {
                "id": "9115903",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=yogesh.ramprabhu",
                    "name": "yogesh.ramprabhu",
                    "key": "JIRAUSER87206",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10346",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10346",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10346",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10346"
                    },
                    "displayName": "Yogesh Ramprabhu",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:12:35.000+0530",
                "items": [
                    {
                        "field": "status",
                        "fieldtype": "jira",
                        "from": "4",
                        "fromString": "Reopened",
                        "to": "10901",
                        "toString": "Trim Backup Done"
                    }
                ]
            },
            {
                "id": "9115949",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                    "name": "archives.bot",
                    "key": "JIRAUSER97240",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                    },
                    "displayName": "Archives Bot",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:21:52.000+0530",
                "items": [
                    {
                        "field": "status",
                        "fieldtype": "jira",
                        "from": "10901",
                        "fromString": "Trim Backup Done",
                        "to": "4",
                        "toString": "Reopened"
                    }
                ]
            },
            {
                "id": "9115954",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:26:35.000+0530",
                "items": [
                    {
                        "field": "duedate",
                        "fieldtype": "jira",
                        "from": "2025-10-12",
                        "fromString": "2025-10-12 19:12:35.0",
                        "to": "2025-10-12",
                        "toString": "2025-10-12 19:26:34.522"
                    }
                ]
            },
            {
                "id": "9115955",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:26:35.000+0530",
                "items": [
                    {
                        "field": "IE_Trim Backup Done Date",
                        "fieldtype": "custom",
                        "from": "2025-10-09",
                        "fromString": "9/Oct/25",
                        "to": "2025-10-09",
                        "toString": "9/Oct/25"
                    }
                ]
            },
            {
                "id": "9115956",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=admin",
                    "name": "admin",
                    "key": "admin",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=admin&avatarId=20613",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=admin&avatarId=20613",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=admin&avatarId=20613",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=admin&avatarId=20613"
                    },
                    "displayName": "Service Desk Admin",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:26:35.000+0530",
                "items": [
                    {
                        "field": "VP_TrimBackupDoneDate",
                        "fieldtype": "custom",
                        "from": "2025-10-09",
                        "fromString": "9/Oct/25",
                        "to": "2025-10-09",
                        "toString": "9/Oct/25"
                    }
                ]
            },
            {
                "id": "9115957",
                "author": {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=yogesh.ramprabhu",
                    "name": "yogesh.ramprabhu",
                    "key": "JIRAUSER87206",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10346",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10346",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10346",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10346"
                    },
                    "displayName": "Yogesh Ramprabhu",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                "created": "2025-10-09T19:26:35.000+0530",
                "items": [
                    {
                        "field": "status",
                        "fieldtype": "jira",
                        "from": "4",
                        "fromString": "Reopened",
                        "to": "10901",
                        "toString": "Trim Backup Done"
                    }
                ]
            }
        ]
    },
    "versionedRepresentations": {
        "customfield_71100": {
            "1": null
        },
        "customfield_65100": {
            "1": null
        },
        "resolution": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/resolution/10600",
                "id": "10600",
                "description": "",
                "name": "Pending Approval"
            }
        },
        "customfield_112207": {
            "1": null
        },
        "customfield_110027": {
            "1": null
        },
        "lastViewed": {
            "1": null
        },
        "customfield_100102": {
            "1": null
        },
        "customfield_100101": {
            "1": null
        },
        "customfield_108174": {
            "1": null
        },
        "labels": {
            "1": []
        },
        "customfield_36900": {
            "1": null
        },
        "customfield_109700": {
            "1": null
        },
        "customfield_107521": {
            "1": null
        },
        "customfield_63704": {
            "1": null
        },
        "customfield_11702": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11301",
                "value": "Normal",
                "id": "11301",
                "disabled": false
            }
        },
        "customfield_107520": {
            "1": "Who Is Sadhguru Really?"
        },
        "customfield_11704": {
            "1": null
        },
        "aggregatetimeoriginalestimate": {
            "1": 0
        },
        "customfield_58003": {
            "1": null
        },
        "customfield_107524": {
            "1": null
        },
        "customfield_10618": {
            "1": null
        },
        "customfield_10619": {
            "1": null
        },
        "issuelinks": {
            "1": [
                {
                    "id": "234936",
                    "self": "https://servicedesk.isha.in/rest/api/2/issueLink/234936",
                    "type": {
                        "id": "10003",
                        "name": "Relates",
                        "inward": "relates to",
                        "outward": "relates to",
                        "self": "https://servicedesk.isha.in/rest/api/2/issueLinkType/10003"
                    },
                    "inwardIssue": {
                        "id": "758496",
                        "key": "OCD-20113",
                        "self": "https://servicedesk.isha.in/rest/api/2/issue/758496",
                        "fields": {
                            "summary": "[VP] [IOS] How to Experience the Real Sadhguru?",
                            "status": {
                                "self": "https://servicedesk.isha.in/rest/api/2/status/22523",
                                "description": "",
                                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/generic.png",
                                "name": "Queued for Final Approval",
                                "id": "22523",
                                "statusCategory": {
                                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/3",
                                    "id": 3,
                                    "key": "done",
                                    "colorName": "success",
                                    "name": "Done"
                                }
                            },
                            "priority": {
                                "self": "https://servicedesk.isha.in/rest/api/2/priority/10101",
                                "iconUrl": "https://servicedesk.isha.in/images/icons/priorities/minor.svg",
                                "name": "Normal",
                                "id": "10101"
                            },
                            "issuetype": {
                                "self": "https://servicedesk.isha.in/rest/api/2/issuetype/15802",
                                "id": "15802",
                                "description": "",
                                "iconUrl": "https://servicedesk.isha.in/secure/viewavatar?size=xsmall&avatarId=10300&avatarType=issuetype",
                                "name": "OCD Video Edit",
                                "subtask": false,
                                "avatarId": 10300
                            }
                        }
                    }
                }
            ]
        },
        "assignee": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=venugopal.reddy",
                "name": "venugopal.reddy",
                "key": "JIRAUSER89217",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=JIRAUSER89217&avatarId=21106",
                    "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=JIRAUSER89217&avatarId=21106",
                    "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=JIRAUSER89217&avatarId=21106",
                    "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=JIRAUSER89217&avatarId=21106"
                },
                "displayName": "venugopalreddy Gajjela",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "customfield_63700": {
            "1": null
        },
        "customfield_110370": {
            "1": null
        },
        "customfield_63703": {
            "1": null
        },
        "customfield_23600": {
            "1": null
        },
        "components": {
            "1": []
        },
        "customfield_108400": {
            "1": null
        },
        "customfield_10605": {
            "1": null
        },
        "customfield_10606": {
            "1": null
        },
        "customfield_108529": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/32371",
                "value": "Sadhguru",
                "id": "32371",
                "disabled": false
            }
        },
        "customfield_109859": {
            "1": null
        },
        "customfield_64701": {
            "1": "2025-08-14T20:17:13.000+0530"
        },
        "subtasks": {
            "1": []
        },
        "customfield_110708": {
            "1": null
        },
        "customfield_110707": {
            "1": null
        },
        "reporter": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=zhang.huiqing",
                "name": "zhang.huiqing",
                "key": "JIRAUSER90310",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=JIRAUSER90310&avatarId=19438",
                    "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=JIRAUSER90310&avatarId=19438",
                    "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=JIRAUSER90310&avatarId=19438",
                    "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=JIRAUSER90310&avatarId=19438"
                },
                "displayName": "Zhang Huiqing",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "customfield_53800": {
            "1": null
        },
        "customfield_107662": {
            "1": null
        },
        "customfield_107663": {
            "1": null
        },
        "customfield_37102": {
            "1": null
        },
        "progress": {
            "1": {
                "progress": 0,
                "total": 0
            }
        },
        "customfield_69703": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=koushik.anand",
                "name": "koushik.anand",
                "key": "JIRAUSER97254",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=48",
                    "24x24": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=24",
                    "16x16": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=16",
                    "32x32": "https://www.gravatar.com/avatar/15e2336699729b00e0676752d7bf8006?d=mm&s=32"
                },
                "displayName": "Koushik Anand",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "customfield_108518": {
            "1": null
        },
        "votes": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/issue/VP-49947/votes",
                "votes": 0,
                "hasVoted": false
            }
        },
        "worklog": {
            "1": {
                "startAt": 0,
                "maxResults": 20,
                "total": 0,
                "worklogs": []
            }
        },
        "customfield_110396": {
            "1": null
        },
        "archivedby": {
            "1": null
        },
        "customfield_65900": {
            "1": null
        },
        "customfield_65903": {
            "1": "2025-08-05T05:30:00.000+0530"
        },
        "customfield_65902": {
            "1": null
        },
        "issuetype": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/issuetype/15200",
                "id": "15200",
                "description": "",
                "iconUrl": "https://servicedesk.isha.in/secure/viewavatar?size=xsmall&avatarId=10300&avatarType=issuetype",
                "name": "Trim Backup Issue Type",
                "subtask": false,
                "avatarId": 10300
            }
        },
        "customfield_67202": {
            "1": null
        },
        "project": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/project/11000",
                "id": "11000",
                "key": "VP",
                "name": "Video Publications",
                "projectTypeKey": "service_desk",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/projectavatar?pid=11000&avatarId=13401",
                    "24x24": "https://servicedesk.isha.in/secure/projectavatar?size=small&pid=11000&avatarId=13401",
                    "16x16": "https://servicedesk.isha.in/secure/projectavatar?size=xsmall&pid=11000&avatarId=13401",
                    "32x32": "https://servicedesk.isha.in/secure/projectavatar?size=medium&pid=11000&avatarId=13401"
                }
            }
        },
        "customfield_12211": {
            "1": null
        },
        "customfield_12210": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11616",
                    "value": "MP4",
                    "id": "11616",
                    "disabled": false
                }
            ]
        },
        "customfield_12213": {
            "1": null
        },
        "customfield_12212": {
            "1": null
        },
        "customfield_12214": {
            "1": null
        },
        "customfield_109513": {
            "1": null
        },
        "customfield_109876": {
            "1": null
        },
        "customfield_12205": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11606",
                    "value": "For All",
                    "id": "11606",
                    "disabled": false
                }
            ]
        },
        "customfield_109875": {
            "1": null
        },
        "customfield_12207": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21506",
                    "value": "Instagram",
                    "id": "21506",
                    "disabled": false
                }
            ]
        },
        "customfield_109517": {
            "1": null
        },
        "customfield_102201": {
            "1": "2025-10-01"
        },
        "customfield_109515": {
            "1": null
        },
        "customfield_107699": {
            "1": null
        },
        "resolutiondate": {
            "1": "2025-10-01T12:36:35.000+0530"
        },
        "customfield_60001": {
            "1": null
        },
        "customfield_102202": {
            "1": "'/Volumes/vnas2/epub/Content Review/OCD-20113'"
        },
        "customfield_109514": {
            "1": null
        },
        "customfield_83801": {
            "1": null
        },
        "watches": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/issue/VP-49947/watchers",
                "watchCount": 0,
                "isWatching": false
            }
        },
        "customfield_12202": {
            "1": "IOS 9\n\nEpub\n\nHow to Experience the Real Sadhguru?\n\nE-media suggested title\n\nEpub suggested title\n\nContent\n\nM531 \n\nScript\n\nQuestion: how to experience the real Sadhguru?\nSadhguru: So, what is it that you're calling as \"Sadhguru\"? That which can guide you beyond teachings, that which can lead you beyond logic, that which can make way for you beyond all reason because what we think is logic, what we think is our reasoning, all this can only happen within the framework of the data that we have gathered. We know a few things; using our logic we can multiply it in many different ways, but the same things.\nthe same old things will repeat in many different ways. This is what we call as karma. \nSo if you want something fresh to happen, you need a fresh injection of life which is not about what you know, it is not a philosophy, it is not an ideology, it is not a religion, it is not a belief system, it is not in the framework of your knowledge or your understanding or your present perception. If you find that – that \"Sadhguru\". What is your prarabdha, what is the prescribed life for you? When somebody takes you beyond that, you call that person \"Sadhguru\".\nSo... well, it's on. When it's still on – it's not going to be on forever, it's still on and I live dangerously (Laughs) – so when it's still on, make it happen for yourself. It's my wish and my blessing. Every one of you should know this.\n\nE-media comment\n\nthis is a very good concept, can we find some more profound content about this topic.\n\nE-pub comment\n\nSwami Harsha's Comment\n\nOK\n\nGCP comment\n\nbeautiful content"
        },
        "customfield_12201": {
            "1": null
        },
        "customfield_12204": {
            "1": null
        },
        "customfield_109502": {
            "1": null
        },
        "customfield_109501": {
            "1": null
        },
        "customfield_109500": {
            "1": null
        },
        "customfield_111700": {
            "1": "org.hakanai.jira.plugins.StatusColor@2c92d9a"
        },
        "customfield_109869": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35033",
                "value": "No",
                "id": "35033",
                "disabled": false
            }
        },
        "customfield_109505": {
            "1": null
        },
        "customfield_109504": {
            "1": null
        },
        "customfield_109503": {
            "1": null
        },
        "customfield_106913": {
            "1": null
        },
        "customfield_106914": {
            "1": null
        },
        "customfield_109509": {
            "1": null
        },
        "customfield_109508": {
            "1": null
        },
        "customfield_106912": {
            "1": null
        },
        "customfield_109507": {
            "1": null
        },
        "updated": {
            "1": "2025-10-09T19:26:35.000+0530"
        },
        "customfield_106915": {
            "1": null
        },
        "customfield_63420": {
            "1": null
        },
        "timeoriginalestimate": {
            "1": 0
        },
        "description": {
            "1": "[VP] [IOS] How to Experience the Real Sadhguru?"
        },
        "customfield_109652": {
            "1": null
        },
        "customfield_108200": {
            "1": null
        },
        "customfield_109651": {
            "1": null
        },
        "customfield_109650": {
            "1": null
        },
        "timetracking": {
            "1": {
                "originalEstimate": "0h",
                "remainingEstimate": "0h",
                "timeSpent": "0h",
                "originalEstimateSeconds": 0,
                "remainingEstimateSeconds": 0,
                "timeSpentSeconds": 0
            }
        },
        "customfield_112824": {
            "1": null
        },
        "customfield_63418": {
            "1": null
        },
        "customfield_10006": {
            "1": null
        },
        "customfield_112825": {
            "1": null
        },
        "customfield_10007": {
            "1": null
        },
        "customfield_112822": {
            "1": null
        },
        "customfield_108202": {
            "1": null
        },
        "customfield_112823": {
            "1": null
        },
        "customfield_109653": {
            "1": null
        },
        "customfield_10009": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/10327",
                "value": "E-Media",
                "id": "10327",
                "disabled": false
            }
        },
        "customfield_106700": {
            "1": null
        },
        "customfield_109417": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36239",
                "value": "Reel",
                "id": "36239",
                "disabled": false
            }
        },
        "customfield_63410": {
            "1": null
        },
        "customfield_63411": {
            "1": null
        },
        "customfield_63413": {
            "1": null
        },
        "customfield_109419": {
            "1": null
        },
        "customfield_22900": {
            "1": null
        },
        "customfield_63414": {
            "1": null
        },
        "customfield_63415": {
            "1": null
        },
        "customfield_63416": {
            "1": null
        },
        "summary": {
            "1": "[VP] [IOS] How to Experience the Real Sadhguru?-Trim Backup"
        },
        "customfield_10000": {
            "1": {
                "errorMessage": "The request type you are trying to view does not exist.",
                "i18nErrorMessage": {
                    "i18nKey": "sd.customerview.error.requestTypeNotFound",
                    "parameters": []
                }
            }
        },
        "customfield_10001": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=maa.oni",
                    "name": "maa.oni",
                    "key": "roop.shekhawat",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://www.gravatar.com/avatar/a01089396efffe7dec38c194ed5240b6?d=mm&s=48",
                        "24x24": "https://www.gravatar.com/avatar/a01089396efffe7dec38c194ed5240b6?d=mm&s=24",
                        "16x16": "https://www.gravatar.com/avatar/a01089396efffe7dec38c194ed5240b6?d=mm&s=16",
                        "32x32": "https://www.gravatar.com/avatar/a01089396efffe7dec38c194ed5240b6?d=mm&s=32"
                    },
                    "displayName": "Maa Oni",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=emedia.reel%40ishafoundation.org",
                    "name": "<EMAIL>",
                    "key": "JIRAUSER93552",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://www.gravatar.com/avatar/564bc5c60372cbe4f3db4dc2ae9742a4?d=mm&s=48",
                        "24x24": "https://www.gravatar.com/avatar/564bc5c60372cbe4f3db4dc2ae9742a4?d=mm&s=24",
                        "16x16": "https://www.gravatar.com/avatar/564bc5c60372cbe4f3db4dc2ae9742a4?d=mm&s=16",
                        "32x32": "https://www.gravatar.com/avatar/564bc5c60372cbe4f3db4dc2ae9742a4?d=mm&s=32"
                    },
                    "displayName": "Emedia.reel",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=rishi.sahu",
                    "name": "rishi.sahu",
                    "key": "JIRAUSER95647",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://www.gravatar.com/avatar/51beeb4a46df997202cb76c20a9e87d8?d=mm&s=48",
                        "24x24": "https://www.gravatar.com/avatar/51beeb4a46df997202cb76c20a9e87d8?d=mm&s=24",
                        "16x16": "https://www.gravatar.com/avatar/51beeb4a46df997202cb76c20a9e87d8?d=mm&s=16",
                        "32x32": "https://www.gravatar.com/avatar/51beeb4a46df997202cb76c20a9e87d8?d=mm&s=32"
                    },
                    "displayName": "Rishi Sahu",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=ching.chen",
                    "name": "ching.chen",
                    "key": "JIRAUSER87014",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=JIRAUSER87014&avatarId=18700",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=JIRAUSER87014&avatarId=18700",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=JIRAUSER87014&avatarId=18700",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=JIRAUSER87014&avatarId=18700"
                    },
                    "displayName": "ching chen",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=diksha.taank",
                    "name": "diksha.taank",
                    "key": "JIRAUSER92457",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=JIRAUSER92457&avatarId=20609",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=JIRAUSER92457&avatarId=20609",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=JIRAUSER92457&avatarId=20609",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=JIRAUSER92457&avatarId=20609"
                    },
                    "displayName": "Diksha Taank",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=aarushi.keshot",
                    "name": "aarushi.keshot",
                    "key": "JIRAUSER97105",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=JIRAUSER97105&avatarId=22126",
                        "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=JIRAUSER97105&avatarId=22126",
                        "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=JIRAUSER97105&avatarId=22126",
                        "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=JIRAUSER97105&avatarId=22126"
                    },
                    "displayName": "Aarushi Keshot",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/user?username=surenkeerthy.r",
                    "name": "surenkeerthy.r",
                    "key": "surenkeerthy.r",
                    "emailAddress": "<EMAIL>",
                    "avatarUrls": {
                        "48x48": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=48",
                        "24x24": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=24",
                        "16x16": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=16",
                        "32x32": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=32"
                    },
                    "displayName": "surenkeerthy r.",
                    "active": true,
                    "timeZone": "Asia/Kolkata"
                }
            ]
        },
        "customfield_12301": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/11707",
                    "value": "English",
                    "id": "11707",
                    "disabled": false
                }
            ]
        },
        "customfield_10002": {
            "1": {
                "id": "19",
                "name": "Time to resolution",
                "_links": {
                    "self": "https://servicedesk.isha.in/rest/servicedeskapi/request/784197/sla/19"
                },
                "completedCycles": [],
                "ongoingCycle": {
                    "startTime": {
                        "iso8601": "2025-10-01T12:36:36+0530",
                        "jira": "2025-10-01T12:36:36.000+0530",
                        "friendly": "01/Oct/25 12:36 PM",
                        "epochMillis": 1759302396000
                    },
                    "breachTime": {
                        "iso8601": "2025-10-22T12:36:36+0530",
                        "jira": "2025-10-22T12:36:36.000+0530",
                        "friendly": "22/Oct/25 12:36 PM",
                        "epochMillis": 1761116796000
                    },
                    "breached": false,
                    "paused": false,
                    "withinCalendarHours": false,
                    "goalDuration": {
                        "millis": 432000000,
                        "friendly": "120h"
                    },
                    "elapsedTime": {
                        "millis": 188604000,
                        "friendly": "52h 23m"
                    },
                    "remainingTime": {
                        "millis": 243396000,
                        "friendly": "67h 36m"
                    }
                }
            }
        },
        "customfield_10003": {
            "1": {
                "errorMessage": "metric not found"
            }
        },
        "customfield_110508": {
            "1": null
        },
        "customfield_107103": {
            "1": null
        },
        "customfield_63407": {
            "1": null
        },
        "customfield_107104": {
            "1": null
        },
        "customfield_63408": {
            "1": null
        },
        "customfield_109523": {
            "1": "2025-10-04"
        },
        "environment": {
            "1": null
        },
        "customfield_109643": {
            "1": null
        },
        "customfield_63409": {
            "1": null
        },
        "customfield_79300": {
            "1": null
        },
        "customfield_109642": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/34404",
                "value": "Sadhguru Reach",
                "id": "34404",
                "disabled": false
            }
        },
        "customfield_109649": {
            "1": null
        },
        "customfield_111600": {
            "1": []
        },
        "customfield_109648": {
            "1": null
        },
        "customfield_106811": {
            "1": null
        },
        "duedate": {
            "1": "2025-10-12"
        },
        "customfield_109647": {
            "1": "2025-09-05"
        },
        "customfield_112137": {
            "1": null
        },
        "customfield_109646": {
            "1": null
        },
        "customfield_112134": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/39096",
                "value": "Regular",
                "id": "39096",
                "disabled": false
            }
        },
        "customfield_112135": {
            "1": null
        },
        "customfield_63400": {
            "1": null
        },
        "comment": {
            "1": {
                "comments": [
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/issue/784197/comment/3417028",
                        "id": "3417028",
                        "author": {
                            "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                            "name": "archives.bot",
                            "key": "JIRAUSER97240",
                            "emailAddress": "<EMAIL>",
                            "avatarUrls": {
                                "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                                "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                                "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                                "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                            },
                            "displayName": "Archives Bot",
                            "active": true,
                            "timeZone": "Asia/Kolkata"
                        },
                        "body": "Issue reopened by admin. Reason: namaskaram,",
                        "updateAuthor": {
                            "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                            "name": "archives.bot",
                            "key": "JIRAUSER97240",
                            "emailAddress": "<EMAIL>",
                            "avatarUrls": {
                                "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                                "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                                "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                                "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                            },
                            "displayName": "Archives Bot",
                            "active": true,
                            "timeZone": "Asia/Kolkata"
                        },
                        "created": "2025-10-09T19:05:27.000+0530",
                        "updated": "2025-10-09T19:05:27.000+0530"
                    },
                    {
                        "self": "https://servicedesk.isha.in/rest/api/2/issue/784197/comment/3417042",
                        "id": "3417042",
                        "author": {
                            "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                            "name": "archives.bot",
                            "key": "JIRAUSER97240",
                            "emailAddress": "<EMAIL>",
                            "avatarUrls": {
                                "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                                "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                                "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                                "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                            },
                            "displayName": "Archives Bot",
                            "active": true,
                            "timeZone": "Asia/Kolkata"
                        },
                        "body": "Issue reopened by admin. Reason: namaskaram",
                        "updateAuthor": {
                            "self": "https://servicedesk.isha.in/rest/api/2/user?username=archives.bot",
                            "name": "archives.bot",
                            "key": "JIRAUSER97240",
                            "emailAddress": "<EMAIL>",
                            "avatarUrls": {
                                "48x48": "https://servicedesk.isha.in/secure/useravatar?avatarId=10349",
                                "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&avatarId=10349",
                                "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&avatarId=10349",
                                "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&avatarId=10349"
                            },
                            "displayName": "Archives Bot",
                            "active": true,
                            "timeZone": "Asia/Kolkata"
                        },
                        "created": "2025-10-09T19:21:55.000+0530",
                        "updated": "2025-10-09T19:21:55.000+0530"
                    }
                ],
                "maxResults": 2,
                "total": 2,
                "startAt": 0
            }
        },
        "customfield_63401": {
            "1": null
        },
        "customfield_63402": {
            "1": null
        },
        "customfield_63404": {
            "1": null
        },
        "customfield_63405": {
            "1": null
        },
        "customfield_63406": {
            "1": null
        },
        "customfield_69301": {
            "1": null
        },
        "customfield_50802": {
            "1": null
        },
        "customfield_69300": {
            "1": "OCD-20113_How_to_Experience_the_Real_Sadhguru_IOS_EngR"
        },
        "customfield_69303": {
            "1": null
        },
        "customfield_69302": {
            "1": null
        },
        "fixVersions": {
            "1": []
        },
        "customfield_109552": {
            "1": "-12.0"
        },
        "customfield_108100": {
            "1": null
        },
        "customfield_106722": {
            "1": null
        },
        "customfield_109439": {
            "1": null
        },
        "customfield_106723": {
            "1": null
        },
        "customfield_109438": {
            "1": "2025-10-09"
        },
        "customfield_109437": {
            "1": null
        },
        "customfield_106721": {
            "1": null
        },
        "customfield_112163": {
            "1": null
        },
        "customfield_76404": {
            "1": null
        },
        "customfield_76403": {
            "1": null
        },
        "customfield_76402": {
            "1": null
        },
        "priority": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/priority/10101",
                "iconUrl": "https://servicedesk.isha.in/images/icons/priorities/minor.svg",
                "name": "Normal",
                "id": "10101"
            }
        },
        "customfield_112828": {
            "1": null
        },
        "customfield_109663": {
            "1": null
        },
        "customfield_109421": {
            "1": null
        },
        "customfield_107001": {
            "1": null
        },
        "customfield_112829": {
            "1": null
        },
        "customfield_109420": {
            "1": null
        },
        "customfield_112826": {
            "1": null
        },
        "customfield_112827": {
            "1": null
        },
        "customfield_112835": {
            "1": null
        },
        "customfield_112836": {
            "1": null
        },
        "customfield_112833": {
            "1": null
        },
        "customfield_109423": {
            "1": null
        },
        "customfield_112834": {
            "1": null
        },
        "customfield_109422": {
            "1": null
        },
        "customfield_112831": {
            "1": null
        },
        "timeestimate": {
            "1": 0
        },
        "versions": {
            "1": []
        },
        "customfield_112832": {
            "1": null
        },
        "customfield_107006": {
            "1": null
        },
        "customfield_112830": {
            "1": null
        },
        "customfield_106710": {
            "1": null
        },
        "customfield_63421": {
            "1": null
        },
        "customfield_63422": {
            "1": null
        },
        "customfield_63423": {
            "1": null
        },
        "customfield_106713": {
            "1": null
        },
        "customfield_63425": {
            "1": null
        },
        "customfield_63426": {
            "1": null
        },
        "status": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/status/10901",
                "description": "",
                "iconUrl": "https://servicedesk.isha.in/images/icons/statuses/generic.png",
                "name": "Trim Backup Done",
                "id": "10901",
                "statusCategory": {
                    "self": "https://servicedesk.isha.in/rest/api/2/statuscategory/4",
                    "id": 4,
                    "key": "indeterminate",
                    "colorName": "inprogress",
                    "name": "In Progress"
                }
            }
        },
        "customfield_108123": {
            "1": null
        },
        "customfield_108000": {
            "1": null
        },
        "archiveddate": {
            "1": null
        },
        "customfield_107037": {
            "1": null
        },
        "customfield_62801": {
            "1": "closing for trim. videonot yet approved."
        },
        "customfield_110204": {
            "1": "Issue reopened by admin. Reason: namaskaram"
        },
        "customfield_62800": {
            "1": null
        },
        "customfield_109335": {
            "1": "Z:\\_trimbackup\\Koushik anand\\OCD-20113_How_To_Experience_Real_Sadhguru"
        },
        "customfield_107035": {
            "1": null
        },
        "customfield_108124": {
            "1": null
        },
        "customfield_106744": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=surenkeerthy.r",
                "name": "surenkeerthy.r",
                "key": "surenkeerthy.r",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=48",
                    "24x24": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=24",
                    "16x16": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=16",
                    "32x32": "https://www.gravatar.com/avatar/9b8cf4b0015823474539affee656764a?d=mm&s=32"
                },
                "displayName": "surenkeerthy r.",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "aggregatetimeestimate": {
            "1": 0
        },
        "customfield_106743": {
            "1": null
        },
        "customfield_64302": {
            "1": null
        },
        "customfield_62802": {
            "1": null
        },
        "creator": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=lakshmi.cr",
                "name": "lakshmi.cr",
                "key": "lakshmi.cr",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=lakshmi.cr&avatarId=20886",
                    "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=lakshmi.cr&avatarId=20886",
                    "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=lakshmi.cr&avatarId=20886",
                    "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=lakshmi.cr&avatarId=20886"
                },
                "displayName": "lakshmi cr.",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "aggregateprogress": {
            "1": {
                "progress": 0,
                "total": 0
            }
        },
        "customfield_107022": {
            "1": null
        },
        "customfield_10200": {
            "1": null
        },
        "customfield_11403": {
            "1": null
        },
        "customfield_109203": {
            "1": ""
        },
        "customfield_108114": {
            "1": null
        },
        "customfield_109202": {
            "1": null
        },
        "customfield_107822": {
            "1": null
        },
        "customfield_109206": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=lakshmi.cr",
                "name": "lakshmi.cr",
                "key": "lakshmi.cr",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=lakshmi.cr&avatarId=20886",
                    "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=lakshmi.cr&avatarId=20886",
                    "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=lakshmi.cr&avatarId=20886",
                    "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=lakshmi.cr&avatarId=20886"
                },
                "displayName": "lakshmi cr.",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "customfield_65501": {
            "1": null
        },
        "customfield_65500": {
            "1": null
        },
        "customfield_63202": {
            "1": "2025-08-14T20:17:13.000+0530"
        },
        "customfield_63203": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/21501",
                    "value": "Stems",
                    "id": "21501",
                    "disabled": false
                }
            ]
        },
        "customfield_65502": {
            "1": null
        },
        "customfield_112173": {
            "1": null
        },
        "customfield_65301": {
            "1": "/Volumes/vnas2/video/koushik.anand/OCD-20113_How_to_Experience_the_Real_Sadhguru_IOS_EngR"
        },
        "customfield_63001": {
            "1": null
        },
        "timespent": {
            "1": 0
        },
        "aggregatetimespent": {
            "1": 0
        },
        "customfield_10311": {
            "1": null
        },
        "customfield_108022": {
            "1": null
        },
        "customfield_10312": {
            "1": null
        },
        "customfield_108021": {
            "1": null
        },
        "customfield_108028": {
            "1": null
        },
        "customfield_107610": {
            "1": null
        },
        "customfield_107057": {
            "1": null
        },
        "customfield_108025": {
            "1": null
        },
        "customfield_10308": {
            "1": null
        },
        "customfield_109911": {
            "1": null
        },
        "customfield_107611": {
            "1": null
        },
        "customfield_10309": {
            "1": null
        },
        "customfield_107612": {
            "1": null
        },
        "customfield_68802": {
            "1": null
        },
        "workratio": {
            "1": 0
        },
        "customfield_68800": {
            "1": null
        },
        "customfield_68801": {
            "1": null
        },
        "customfield_41201": {
            "1": null
        },
        "created": {
            "1": "2025-10-01T12:36:36.000+0530"
        },
        "customfield_108134": {
            "1": "2025-10-09"
        },
        "customfield_108133": {
            "1": null
        },
        "customfield_10300": {
            "1": null
        },
        "customfield_10301": {
            "1": null
        },
        "customfield_70900": {
            "1": null
        },
        "customfield_110336": {
            "1": null
        },
        "customfield_107723": {
            "1": null
        },
        "customfield_109902": {
            "1": null
        },
        "customfield_107724": {
            "1": null
        },
        "customfield_107603": {
            "1": null
        },
        "customfield_106756": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/29963",
                "value": "English",
                "id": "29963",
                "disabled": false
            }
        },
        "customfield_110335": {
            "1": null
        },
        "customfield_106754": {
            "1": null
        },
        "customfield_107601": {
            "1": null
        },
        "customfield_107606": {
            "1": null
        },
        "customfield_107607": {
            "1": null
        },
        "customfield_107725": {
            "1": null
        },
        "customfield_106757": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/user?username=poornima.p",
                "name": "poornima.p",
                "key": "poornima.p",
                "emailAddress": "<EMAIL>",
                "avatarUrls": {
                    "48x48": "https://servicedesk.isha.in/secure/useravatar?ownerId=poornima.p&avatarId=20141",
                    "24x24": "https://servicedesk.isha.in/secure/useravatar?size=small&ownerId=poornima.p&avatarId=20141",
                    "16x16": "https://servicedesk.isha.in/secure/useravatar?size=xsmall&ownerId=poornima.p&avatarId=20141",
                    "32x32": "https://servicedesk.isha.in/secure/useravatar?size=medium&ownerId=poornima.p&avatarId=20141"
                },
                "displayName": "poornima p.",
                "active": true,
                "timeZone": "Asia/Kolkata"
            }
        },
        "customfield_109903": {
            "1": null
        },
        "customfield_21304": {
            "1": null
        },
        "customfield_109909": {
            "1": null
        },
        "customfield_21303": {
            "1": null
        },
        "customfield_107608": {
            "1": null
        },
        "customfield_21302": {
            "1": null
        },
        "customfield_107609": {
            "1": null
        },
        "customfield_21300": {
            "1": []
        },
        "customfield_69001": {
            "1": null
        },
        "customfield_69000": {
            "1": null
        },
        "customfield_17602": {
            "1": null
        },
        "customfield_110006": {
            "1": null
        },
        "attachment": {
            "1": []
        },
        "customfield_10405": {
            "1": null
        },
        "customfield_107519": {
            "1": null
        },
        "customfield_26400": {
            "1": null
        },
        "customfield_110000": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/35414",
                "value": "Wisdom Reel - SG",
                "id": "35414",
                "disabled": false
            }
        },
        "customfield_64900": {
            "1": null
        },
        "customfield_109819": {
            "1": null
        },
        "customfield_65310": {
            "1": null
        },
        "customfield_64100": {
            "1": "Who Is Sadhguru Really?"
        },
        "customfield_64101": {
            "1": null
        },
        "customfield_65311": {
            "1": "n/a"
        },
        "customfield_108031": {
            "1": "00:01:00"
        },
        "customfield_108030": {
            "1": null
        },
        "customfield_107620": {
            "1": null
        },
        "customfield_10512": {
            "1": null
        },
        "customfield_110355": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36139",
                "value": "2025",
                "id": "36139",
                "disabled": false
            }
        },
        "customfield_107625": {
            "1": null
        },
        "customfield_110356": {
            "1": {
                "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/36149",
                "value": "Sep",
                "id": "36149",
                "disabled": false
            }
        },
        "customfield_65303": {
            "1": null
        },
        "customfield_65302": {
            "1": null
        },
        "customfield_107629": {
            "1": null
        },
        "customfield_65305": {
            "1": [
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22213",
                    "value": "Project File shared",
                    "id": "22213",
                    "disabled": false
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22214",
                    "value": "Project File Opening fine",
                    "id": "22214",
                    "disabled": false
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22215",
                    "value": "XML File added",
                    "id": "22215",
                    "disabled": false
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22216",
                    "value": "Edited Transcript Shared",
                    "id": "22216",
                    "disabled": false
                },
                {
                    "self": "https://servicedesk.isha.in/rest/api/2/customFieldOption/22217",
                    "value": "No Logo Output Added",
                    "id": "22217",
                    "disabled": false
                }
            ]
        },
        "customfield_107626": {
            "1": null
        },
        "customfield_65304": {
            "1": null
        },
        "customfield_65307": {
            "1": null
        },
        "customfield_65308": {
            "1": null
        },
        "customfield_21401": {
            "1": null
        }
    }
}

D:\backup archives trims flow\New folder (2)\extras>