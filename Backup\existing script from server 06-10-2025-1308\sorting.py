import requests
from requests.auth import HTTPBasicAuth
import json
import time
import datetime
import base64
import urllib.parse
import traceback
from collections import defaultdict
import hashlib
import os

# ############## SHEET-LOG START ##############
import gspread
from google.oauth2.service_account import Credentials

SPREADSHEET_ID = '1x8MXW7VfNjufzi0pXeIaXgg5LBUyNc24uWb1X8P--X0'
SHEET_NAME = 'sorting script'
_sheet_cache = None
# ############## SHEET-LOG END ##############

# ============================================================================
# CONFIGURATION
# ============================================================================

totalupdates = 0

# OPTIMIZED: Extend processing window to avoid repeated operations
time_now = datetime.datetime.now()
time_five_minutes_ago = time_now - datetime.timedelta(minutes=5)  # Extended for stability

# JQL time windows
time_now_jql = time_now.strftime("%Y-%m-%d %H:%M")
time_one_minute_ago_jql = (time_now - datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M")

# Changelog search window
time_changelog_start = time_five_minutes_ago.replace(second=0, microsecond=0)
time_changelog_end = time_now.replace(second=59, microsecond=999999)

# Confluence configuration
conf_username = 'script'
conf_password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'

# JIRA configuration
jira_url = "https://servicedesk.isha.in"
decoded_str = base64.b64decode('YXJjaGl2ZXMuYm90OkIwdHRsZQ==').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

# Session for connection pooling
session = requests.Session()
session.headers.update({'User-Agent': 'JIRA-Confluence-Sync/2.0'})

# ============================================================================
# ENHANCED LOGGING AND TRACKING
# ============================================================================

def log_debug(message):
    """Enhanced logging with timestamp"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] {message}"
    print(full_message)
    try:
        with open('jira-confluence-sync.log', 'a', encoding='utf-8') as f:
            f.write(full_message + '\n')
    except:
        pass

# ############## SHEET-LOG START ##############
def _get_worksheet():
    global _sheet_cache
    if _sheet_cache is None:
        try:
            scope = ['https://www.googleapis.com/auth/spreadsheets',
                     'https://www.googleapis.com/auth/drive']
            creds = Credentials.from_service_account_file('credentials.json', scopes=scope)
            client = gspread.authorize(creds)
            _sheet_cache = client.open_by_key(SPREADSHEET_ID).worksheet(SHEET_NAME)
            log_debug("✅ Google Sheets connection established successfully")
        except Exception as e:
            log_debug(f"❌ Failed to connect to Google Sheets: {str(e)}")
            raise e
    return _sheet_cache

def append_log(row):
    """Append a single row to Google Sheet"""
    try:
        worksheet = _get_worksheet()
        worksheet.append_row(row, value_input_option='USER_ENTERED')
        log_debug(f'📑 Successfully logged to Google Sheets: {row[4]}')  # row[4] = issue key
        return True
    except Exception as e:
        log_debug(f'❌ Sheet log failed for {row[4] if len(row) > 4 else "unknown"}: {str(e)}')
        return False

def test_google_sheets_connection():
    """Test Google Sheets connection at startup"""
    try:
        log_debug("🔍 Testing Google Sheets connection...")
        worksheet = _get_worksheet()
        # Try to get the first row to test connection
        first_row = worksheet.row_values(1)
        log_debug(f"✅ Google Sheets connection successful. Headers: {first_row}")
        return True
    except Exception as e:
        log_debug(f"❌ Google Sheets connection failed: {str(e)}")
        return False
# ############## SHEET-LOG END ##############

def create_operation_hash(page_id, user, operation):
    """Create unique hash for operation to prevent duplicates"""
    data = f"{page_id}_{user}_{operation}_{datetime.date.today()}"
    return hashlib.md5(data.encode()).hexdigest()

def check_recent_operation(operation_hash):
    """Check if operation was performed recently"""
    operations_file = 'recent_operations.txt'

    # Clean old operations (older than 30 minutes)
    if os.path.exists(operations_file):
        try:
            with open(operations_file, 'r') as f:
                lines = f.readlines()
            current_time = time.time()
            valid_lines = []
            for line in lines:
                parts = line.strip().split('|')
                if len(parts) == 2:
                    timestamp = float(parts[1])
                    if current_time - timestamp < 1800:  # 30 minutes
                        valid_lines.append(line)
            with open(operations_file, 'w') as f:
                f.writelines(valid_lines)
        except Exception as e:
            log_debug(f"⚠️ Error cleaning operations file: {str(e)}")

    # Check if operation exists
    try:
        if os.path.exists(operations_file):
            with open(operations_file, 'r') as f:
                for line in f:
                    if line.startswith(operation_hash):
                        return True
    except Exception:
        pass

    return False

def record_operation(operation_hash):
    """Record operation to prevent duplicates"""
    try:
        with open('recent_operations.txt', 'a') as f:
            f.write(f"{operation_hash}|{time.time()}\n")
    except Exception as e:
        log_debug(f"⚠️ Error recording operation: {str(e)}")

log_debug("🚀 JIRA-Confluence Permission Sync - PRODUCTION STABLE v4.0")
log_debug(f"⏰ JQL Filter: {time_one_minute_ago_jql} to {time_now_jql}")
log_debug(f"⏰ Changelog Search: {time_changelog_start.strftime('%H:%M:%S')} to {time_changelog_end.strftime('%H:%M:%S')}")

# ============================================================================
# ENHANCED API FUNCTIONS WITH RETRY AND RATE LIMITING
# ============================================================================

def api_request_with_retry(func, *args, max_retries=3, **kwargs):
    """Execute API request with exponential backoff retry"""
    for attempt in range(max_retries):
        try:
            result = func(*args, **kwargs)

            # Handle rate limiting
            if hasattr(result, 'status_code'):
                if result.status_code == 429:
                    retry_after = int(result.headers.get('retry-after', 60))
                    log_debug(f"⚠️ Rate limited, waiting {retry_after} seconds...")
                    time.sleep(retry_after)
                    continue
                elif result.status_code >= 500:
                    wait_time = (2 ** attempt) * 2  # Exponential backoff
                    log_debug(f"⚠️ Server error {result.status_code}, retrying in {wait_time}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue

            return result

        except requests.exceptions.RequestException as e:
            wait_time = (2 ** attempt) * 2
            log_debug(f"⚠️ Request failed: {str(e)}, retrying in {wait_time}s (attempt {attempt + 1}/{max_retries})")
            time.sleep(wait_time)
            if attempt == max_retries - 1:
                raise e

    return None

def convert_userkey_to_username(user_key, jira_key=""):
    """Convert JIRA user key to actual username with caching"""
    if not user_key or user_key.strip() == '':
        return None

    if not user_key.startswith('JIRAUSER'):
        return user_key

    # Simple cache to avoid repeated API calls
    cache_key = f"userkey_cache_{user_key}"
    if hasattr(convert_userkey_to_username, cache_key):
        return getattr(convert_userkey_to_username, cache_key)

    log_debug(f"🔄 {jira_key}: Converting user key '{user_key}'...")

    try:
        user_api_url = f"{jira_url}/rest/api/2/user"
        params = {'key': user_key}
        response = api_request_with_retry(session.get, user_api_url, params=params, auth=auth, timeout=30)

        if response and response.status_code == 200:
            user_data = response.json()
            username = user_data.get('name', user_key)
            display_name = user_data.get('displayName', 'Unknown')

            # Cache result
            setattr(convert_userkey_to_username, cache_key, username)
            log_debug(f"✅ {jira_key}: '{user_key}' → '{username}' ({display_name})")
            return username
        else:
            log_debug(f"⚠️ {jira_key}: Failed to convert '{user_key}'")
            return user_key

    except Exception as e:
        log_debug(f"❌ {jira_key}: Error converting '{user_key}': {str(e)}")
        return user_key

# ============================================================================
# OPTIMIZED ASSIGNEE CHANGE DETECTION
# ============================================================================

def parse_jira_datetime(datetime_str):
    """Parse JIRA datetime string with timezone handling"""
    try:
        if '+' in datetime_str or datetime_str.endswith('Z'):
            if '+' in datetime_str:
                dt_part = datetime_str.split('+')[0]
            else:
                dt_part = datetime_str.replace('Z', '')
            return datetime.datetime.strptime(dt_part[:19], '%Y-%m-%dT%H:%M:%S')
        else:
            return datetime.datetime.strptime(datetime_str[:19], '%Y-%m-%dT%H:%M:%S')
    except Exception as e:
        log_debug(f"⚠️ Error parsing datetime '{datetime_str}': {str(e)}")
        return datetime.datetime.strptime(datetime_str[:19], '%Y-%m-%dT%H:%M:%S')

def get_assignee_change_from_history(jira_key):
    """Get assignee changes with intelligent filtering"""
    log_debug(f"\n📜 {jira_key}: Searching for assignee changes...")

    try:
        changelog_url = f"{jira_url}/rest/api/2/issue/{jira_key}?expand=changelog"
        response = api_request_with_retry(session.get, changelog_url, auth=auth, timeout=30)

        if not response or response.status_code != 200:
            log_debug(f"❌ {jira_key}: Failed to get changelog")
            return None

        issue_data = response.json()

        if 'changelog' not in issue_data or 'histories' not in issue_data['changelog']:
            log_debug(f"⚠️ {jira_key}: No changelog data available")
            return None

        log_debug(f"🔍 {jira_key}: Searching window {time_changelog_start.strftime('%H:%M:%S')} to {time_changelog_end.strftime('%H:%M:%S')}")

        # Find ONLY the most recent assignee change within window
        recent_assignee_change = None

        for history in reversed(issue_data['changelog']['histories']):
            change_time_str = history['created']
            try:
                change_time = parse_jira_datetime(change_time_str)

                if time_changelog_start <= change_time <= time_changelog_end:
                    log_debug(f"🕐 {jira_key}: Checking change at {change_time.strftime('%H:%M:%S')}")

                    fields_changed = [item['field'] for item in history['items']]
                    log_debug(f"🔧 {jira_key}: Fields changed: {fields_changed}")

                    for item in history['items']:
                        if item['field'] == 'assignee':
                            old_assignee_display = item.get('fromString', '') or 'Unassigned'
                            new_assignee_display = item.get('toString', '') or 'Unassigned'
                            old_assignee_key = item.get('from', '')
                            new_assignee_key = item.get('to', '')

                            log_debug(f"🎯 {jira_key}: FOUND ASSIGNEE CHANGE!")
                            log_debug(f" 👤 Display: '{old_assignee_display}' → '{new_assignee_display}'")
                            log_debug(f" ⏰ Time: {change_time}")

                            # Convert to usernames
                            old_username = convert_userkey_to_username(old_assignee_key, jira_key)
                            new_username = convert_userkey_to_username(new_assignee_key, jira_key)

                            recent_assignee_change = {
                                'old_username': old_username,
                                'new_username': new_username,
                                'old_display': old_assignee_display,
                                'new_display': new_assignee_display,
                                'change_time': change_time
                            }

                            # Return immediately - we want the most recent only
                            log_debug(f"✅ {jira_key}: Using assignee change from {change_time}")
                            return recent_assignee_change

            except Exception as e:
                log_debug(f"⚠️ {jira_key}: Error parsing change: {str(e)}")
                continue

        log_debug(f"ℹ️ {jira_key}: No assignee changes found in window")
        return None

    except Exception as e:
        log_debug(f"❌ {jira_key}: Error in changelog analysis: {str(e)}")
        return None

# ============================================================================
# CONFLUENCE FUNCTIONS WITH SAFEGUARDS
# ============================================================================

def get_confluence_page_id(jira_key, confluence_link):
    """Get Confluence page ID with caching"""
    if not confluence_link:
        return None

    try:
        title = confluence_link.split('/')[-1].strip()
        if not title:
            return None

        encoded_title = urllib.parse.quote(title)
        url = f'{confUrl}/rest/api/content/?title={encoded_title}'
        response = api_request_with_retry(requests.get, url, auth=HTTPBasicAuth(conf_username, conf_password), timeout=30)

        if response and response.status_code == 200:
            page_data = response.json()
            if 'results' in page_data and len(page_data['results']) > 0:
                page_id = page_data['results'][0]['id']
                log_debug(f'✅ {jira_key}: Found page ID: {page_id}')
                return page_id

        return None

    except Exception as e:
        log_debug(f'❌ {jira_key}: Error getting page ID: {str(e)}')
        return None

def get_current_confluence_users(page_id, jira_key=""):
    """Get current users with permissions"""
    url = f"{confUrl}/rest/api/content/{page_id}/restriction/byOperation"

    try:
        response = api_request_with_retry(requests.get, url, auth=HTTPBasicAuth(conf_username, conf_password), timeout=30)

        if response and response.status_code == 200:
            restrictions_data = response.json()
            current_users = []

            if ('update' in restrictions_data and
                'restrictions' in restrictions_data['update'] and
                'user' in restrictions_data['update']['restrictions']):
                user_list = restrictions_data['update']['restrictions']['user']['results']
                current_users = [user['username'] for user in user_list]

            log_debug(f"👥 {jira_key}: Current users: {current_users}")
            return current_users

        return []

    except Exception as e:
        log_debug(f'❌ {jira_key}: Error getting permissions: {str(e)}')
        return []

def smart_remove_access(page_id, target_user, jira_key=""):
    """Remove access with duplicate detection"""
    if not target_user:
        return True

    # Check if this operation was done recently
    operation_hash = create_operation_hash(page_id, target_user, "remove")
    if check_recent_operation(operation_hash):
        log_debug(f"⚠️ {jira_key}: Remove operation for '{target_user}' was done recently, skipping")
        return True

    log_debug(f'🔒 {jira_key}: REMOVING ACCESS from {target_user}')
    url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={page_id}&user={target_user}'

    try:
        response = api_request_with_retry(requests.post, url, timeout=30)

        if response and response.status_code == 200:
            record_operation(operation_hash)
            log_debug(f"✅ {jira_key}: Successfully removed access from '{target_user}'")
            # CRITICAL: Wait for propagation to avoid conflicts
            time.sleep(2)
            return True
        else:
            log_debug(f"❌ {jira_key}: Removal failed")
            return False

    except Exception as e:
        log_debug(f"❌ {jira_key}: Exception during removal: {str(e)}")
        return False

def smart_grant_access(page_id, target_user, jira_key=""):
    """Grant access with duplicate detection"""
    if not target_user:
        return False

    # Check if this operation was done recently
    operation_hash = create_operation_hash(page_id, target_user, "grant")
    if check_recent_operation(operation_hash):
        log_debug(f"⚠️ {jira_key}: Grant operation for '{target_user}' was done recently, skipping")
        return True

    try:
        current_users = get_current_confluence_users(page_id, jira_key)
        if target_user in current_users:
            log_debug(f"ℹ️ {jira_key}: '{target_user}' already has access")
            return True

        log_debug(f'🔓 {jira_key}: GRANTING ACCESS to {target_user}')

        new_user_list = [{"type": "known", "username": target_user}]
        if target_user != conf_username:
            new_user_list.append({"type": "known", "username": conf_username})

        for user in current_users:
            if user not in [target_user, conf_username]:
                new_user_list.append({"type": "known", "username": user})

        payload = [{"operation": "update", "restrictions": {"user": new_user_list}}]
        url = f"{confUrl}/rest/experimental/content/{page_id}/restriction"
        headers = {'Content-Type': 'application/json'}

        response = api_request_with_retry(
            requests.put, url,
            json=payload,
            auth=HTTPBasicAuth(conf_username, conf_password),
            headers=headers,
            timeout=30
        )

        if response and response.status_code == 200:
            record_operation(operation_hash)
            log_debug(f"✅ {jira_key}: Successfully granted access to '{target_user}'")
            # CRITICAL: Wait for propagation
            time.sleep(2)
            return True
        else:
            log_debug(f"❌ {jira_key}: Grant failed")
            return False

    except Exception as e:
        log_debug(f"❌ {jira_key}: Exception granting access: {str(e)}")
        return False

# ============================================================================
# INTELLIGENT BATCH PROCESSING
# ============================================================================

def fetch_jira_tickets():
    """Fetch JIRA tickets with enhanced filtering"""
    query = 'project = "Ar Transcription" AND type = "Not Transcribed Contents" AND updated >= "' + time_one_minute_ago_jql + '" AND updated < "' + time_now_jql + '"'

    log_debug(f"📋 Primary JQL: {query}")

    try:
        params = {
            'jql': query,
            'maxResults': 50,  # Reduced to avoid overload
            'fields': 'key,summary,assignee,customfield_111118,status'
        }

        response = api_request_with_retry(session.get, api_url, params=params, auth=auth, timeout=60)

        if response and response.status_code == 200:
            data = response.json()
            issues = data['issues']
            log_debug(f"📊 Found {len(issues)} recently updated issues")
            return issues
        else:
            log_debug(f"❌ JIRA API error")
            return []

    except Exception as e:
        log_debug(f"❌ Error fetching tickets: {str(e)}")
        return []

def process_issues_intelligently(issues):
    """Process issues with intelligent safeguards and comprehensive logging"""
    global totalupdates
    log_debug(f"\n🚀 INTELLIGENT PROCESSING of {len(issues)} issues")

    page_operations = defaultdict(list)

    # Analyze each issue
    for issue in issues:
        jira_key = issue['key']
        current_assignee = issue['fields']['assignee']['name'] if issue['fields']['assignee'] else None
        confluence_link = issue['fields'].get('customfield_111118')
        summary = issue['fields']['summary']
        status = issue['fields']['status']['name'] if issue['fields'].get('status') else ''

        log_debug(f"\n📋 {jira_key}: Current assignee = {current_assignee}")

        # Get assignee changes
        change_info = get_assignee_change_from_history(jira_key)
        if change_info is None:
            log_debug(f"ℹ️ {jira_key}: No recent changes, skipping")
            continue

        # Get page ID
        page_id = get_confluence_page_id(jira_key, confluence_link)
        if not page_id:
            log_debug(f"❌ {jira_key}: No page ID, skipping")
            continue

        # Handle current assignee mismatch
        if change_info['new_username'] != current_assignee:
            log_debug(f"⚠️ {jira_key}: Using current assignee '{current_assignee}'")
            change_info['new_username'] = current_assignee

        # Group operations by page
        page_operations[page_id].append({
            'jira_key': jira_key,
            'summary': summary,
            'status': status,
            'change_info': change_info
        })

    # Process each page intelligently
    log_debug(f"\n📊 Processing {len(page_operations)} unique pages")

    for page_id, operations in page_operations.items():
        jira_keys = [op['jira_key'] for op in operations]
        log_debug(f"\n📄 Page {page_id} - Tickets: {', '.join(jira_keys)}")

        users_to_add = set()
        users_to_remove = set()

        # Collect unique operations
        for operation in operations:
            change_info = operation['change_info']
            jira_key = operation['jira_key']

            if change_info['new_username']:
                users_to_add.add(change_info['new_username'])
                log_debug(f" ➕ {jira_key}: Add '{change_info['new_display']}' ({change_info['new_username']})")

            if (change_info['old_username'] and 
                change_info['old_username'] != change_info['new_username']):
                users_to_remove.add(change_info['old_username'])
                log_debug(f" ➖ {jira_key}: Remove '{change_info['old_display']}' ({change_info['old_username']})")

        # Remove conflicts
        users_to_remove = users_to_remove - users_to_add

        # Execute operations with spacing and track results
        remove_results = {}
        add_results = {}

        # Process removals first
        for user in users_to_remove:
            if user:
                remove_results[user] = smart_remove_access(page_id, user, f"Page-{page_id}")
                time.sleep(3)  # Spacing between operations

        # Process additions
        for user in users_to_add:
            if user:
                add_results[user] = smart_grant_access(page_id, user, f"Page-{page_id}")
                time.sleep(3)  # Spacing between operations

        # ############## ENHANCED SHEET-LOG START ##############
        # Log EVERY operation attempt, regardless of success/failure
        date_str = datetime.datetime.now().strftime('%d-%m-%Y')
        time_str = datetime.datetime.now().strftime('%H:%M:%S')
        removed_users_str = ', '.join(users_to_remove) if users_to_remove else ''

        for op in operations:
            # Determine operation success
            new_user = op['change_info']['new_username']
            old_user = op['change_info']['old_username']

            # Check if any operation was attempted and determine success
            operation_attempted = False
            operation_success = True

            if new_user and new_user in add_results:
                operation_attempted = True
                if not add_results[new_user]:
                    operation_success = False

            if old_user and old_user in remove_results:
                operation_attempted = True
                if not remove_results[old_user]:
                    operation_success = False

            # Only log if operations were actually attempted
            if operation_attempted:
                success_status = 'Success' if operation_success else 'Failed'

                row = [
                    date_str,                                    # A: Date
                    time_str,                                    # B: Time
                    success_status,                              # C: Success or Not
                    'Not Transcribed Contents',                  # D: Issue Type
                    op['jira_key'],                             # E: Issue Key
                    op['summary'],                              # F: Summary
                    op['status'],                               # G: JIRA Current Status
                    op['change_info']['old_display'],           # H: JIRA Previous Assignee
                    op['change_info']['new_display'],           # I: JIRA New Assignee
                    page_id,                                    # J: Confluence Page ID
                    op['change_info']['new_username'] or '',    # K: Conf Given Assignee Access
                    op['change_info']['old_username'] or '',    # L: Conf Prev Assignee Access
                    removed_users_str                           # M: Conf Removed Assignee Access
                ]

                # Attempt to log to Google Sheets
                if append_log(row):
                    log_debug(f"✅ {op['jira_key']}: Logged to Google Sheets ({success_status})")
                else:
                    log_debug(f"❌ {op['jira_key']}: Failed to log to Google Sheets")
        # ############## ENHANCED SHEET-LOG END ##############

        # Update total count for successful changes only
        if any(add_results.values()) or any(remove_results.values()):
            totalupdates += 1
            log_debug(f"✅ Page {page_id}: Operations completed")
        else:
            log_debug(f"❌ Page {page_id}: All operations failed")

# ============================================================================
# PRODUCTION MAIN FUNCTION
# ============================================================================

def main():
    """Production-ready main function"""
    global totalupdates
    totalupdates = 0

    log_debug("=" * 100)
    log_debug("🚀 JIRA-CONFLUENCE PERMISSION SYNC - PRODUCTION STABLE v4.0")
    log_debug("=" * 100)
    log_debug("📝 PRODUCTION FEATURES:")
    log_debug(" ✅ INTELLIGENT: Duplicate operation detection")
    log_debug(" ✅ STABLE: API retry with exponential backoff")
    log_debug(" ✅ SAFE: Rate limiting and propagation delays")
    log_debug(" ✅ ROBUST: Comprehensive error handling")
    log_debug(" ✅ OPTIMIZED: Reduced API calls and conflicts")
    log_debug(" ✅ LOGGING: Google Sheets integration with ALL operations")
    log_debug("=" * 100)

    # Test Google Sheets connection first
    if not test_google_sheets_connection():
        log_debug("❌ CRITICAL: Google Sheets connection failed. Continuing without logging...")

    start_time = datetime.datetime.now()

    try:
        jira_issues = fetch_jira_tickets()

        if len(jira_issues) == 0:
            log_debug("✅ No issues to process")
            return

        log_debug(f"\n📝 Processing {len(jira_issues)} issues...")
        process_issues_intelligently(jira_issues)

    except Exception as e:
        log_debug(f"❌ CRITICAL ERROR: {str(e)}")
        log_debug(f" Traceback: {traceback.format_exc()}")

    end_time = datetime.datetime.now()
    execution_time = (end_time - start_time).total_seconds()

    log_debug("\n" + "=" * 100)
    log_debug(f"🎯 PRODUCTION SYNC COMPLETE")
    log_debug(f" 📊 Permission Updates: {totalupdates}")
    log_debug(f" ⏱️ Execution Time: {execution_time:.2f}s")
    log_debug(f" 📋 Issues Processed: {len(jira_issues) if 'jira_issues' in locals() else 0}")
    log_debug("=" * 100)

def log_execution():
    """Log execution summary"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('permission-sync-summary.log', 'a', encoding='utf-8') as f:
            f.write(f'PRODUCTION STABLE v4.0: {timestamp} - Updates: {totalupdates}\n')
    except Exception as e:
        log_debug(f"⚠️ Could not write summary log: {str(e)}")

# ============================================================================
# SCRIPT EXECUTION
# ============================================================================

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_debug("\n⚠️ Script interrupted by user")
    except Exception as e:
        log_debug(f"\n💥 UNEXPECTED ERROR: {str(e)}")
        log_debug(f" Traceback: {traceback.format_exc()}")
    finally:
        log_execution()
        log_debug("\n🏁 PRODUCTION SCRIPT COMPLETED")
