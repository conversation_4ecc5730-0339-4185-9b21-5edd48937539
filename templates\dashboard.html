<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archives TrimsFlow - Enhanced Dashboard</title>

    <!-- Enhanced CDN Loading -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <style>
        /* Enhanced Typography and Base Styles */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
            min-height: 100vh;
            font-size: 16px;
            color: #1a202c;
        }

        /* CRITICAL: Modal Height and Scroll Constraints */
        .modal .modal-dialog {
            max-height: 85vh;
            margin-top: 2vh;
            margin-bottom: 2vh;
            width: 95%;
            max-width: 1400px;
        }

        .modal .modal-content {
            height: 85vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.25);
        }

        .modal .modal-header {
            flex-shrink: 0;
            padding: 1.5rem 2rem;
            border-bottom: 2px solid #e2e8f0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal .modal-body {
            overflow-y: auto;
            flex-grow: 1;
            padding: 1.5rem 2rem;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        .modal .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal .modal-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .modal .modal-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .modal .modal-footer {
            flex-shrink: 0;
            padding: 1.5rem 2rem;
            border-top: 2px solid #e2e8f0;
            background: #f8fafc;
        }

        /* Enhanced Container System */
        .enhanced-container {
            max-width: 100%;
            padding-left: 2rem;
            padding-right: 2rem;
            margin: 0 auto;
        }

        .main-content {
            padding: 2rem 0;
            min-height: calc(100vh - 120px);
        }

        /* Enhanced Header */
        .gradient-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1.5rem 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .icon-container {
            width: 4rem;
            height: 4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .icon-container i {
            font-size: 1.8rem;
            color: white;
            text-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .status-indicator {
            position: relative;
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #10b981;
        }

        .status-indicator::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            background: inherit;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse-ring 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(2.5);
                opacity: 0;
            }
        }

        /* Enhanced KPI Cards */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .kpi-card {
            padding: 2rem;
            border-radius: 1.5rem;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            min-height: 160px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .kpi-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .kpi-number {
            font-size: 2.5rem;
            font-weight: 900;
            line-height: 1;
            margin: 0.5rem 0;
        }

        .kpi-label {
            font-size: 0.9rem;
            font-weight: 600;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Enhanced Table */
        .enhanced-table-container {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .table-header {
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 2px solid #e2e8f0;
        }

        .table-filters {
            padding: 1.5rem 2rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .enhanced-table {
            width: 100%;
        }

        .enhanced-table th {
            padding: 1.5rem 2rem;
            font-weight: 700;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: none;
        }

        .enhanced-table td {
            padding: 1.5rem 2rem;
            vertical-align: middle;
            border-bottom: 1px solid #f7fafc;
        }

        .enhanced-table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* Enhanced Input Fields */
        .enhanced-input, .enhanced-select, .enhanced-textarea {
            min-height: 3rem;
            font-size: 0.95rem;
            padding: 0.75rem 1.25rem;
            border-width: 2px;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            margin-bottom: 1rem;
            width: 100%;
            background: white;
            border-color: #e2e8f0;
        }

        .enhanced-input:focus, .enhanced-select:focus, .enhanced-textarea:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
            outline: none;
        }

        .enhanced-textarea {
            min-height: 6rem;
            resize: vertical;
        }

        /* Enhanced Form Groups */
        .form-field-group {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .form-field-group:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .form-field-group.auto-field {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border-color: #bbf7d0;
            position: relative;
        }

        .form-field-group.auto-field::after {
            content: 'AUTO';
            position: absolute;
            top: -8px;
            right: 1rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* CRITICAL: File Upload Duration Detection Styles */
        .file-upload-section {
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
            border: 2px solid #fb923c;
            position: relative;
        }

        .file-upload-section::after {
            content: 'FILE UPLOAD';
            position: absolute;
            top: -8px;
            right: 1rem;
            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .file-drop-zone {
            border: 2px dashed #fb923c;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.7);
            margin-bottom: 1rem;
            cursor: pointer;
        }

        .file-drop-zone:hover, .file-drop-zone.dragover {
            border-color: #ea580c;
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.02);
        }

        .file-info {
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
            border: 2px solid #10b981;
            border-radius: 1rem;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }

        .duration-result {
            font-size: 1.25rem;
            font-weight: 700;
            color: #059669;
            margin-top: 0.5rem;
        }

        .form-label {
            font-size: 1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Enhanced Buttons */
        .enhanced-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 0.25rem;
            min-height: 2.75rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

        .enhanced-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .enhanced-btn-lg {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            min-height: 3.25rem;
        }

        /* Color Variants */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
        }

        .btn-light {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1a202c;
        }

        /* Enhanced Grid */
        .enhanced-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .enhanced-grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .enhanced-grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .enhanced-grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
        .enhanced-grid-5 { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); }

        /* Enhanced Folder Browser */
        .folder-browser {
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
            background: white;
            border-radius: 0.75rem;
            padding: 0.5rem;
        }

        .folder-browser::-webkit-scrollbar {
            width: 8px;
        }

        .folder-browser::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .folder-browser::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .folder-item {
            transition: all 0.3s ease;
            cursor: pointer;
            border-radius: 0.5rem;
            margin: 0.25rem;
            padding: 0.75rem;
            background: #f8fafc;
            border: 1px solid transparent;
        }

        .folder-item:hover {
            background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
            transform: translateX(5px) scale(1.01);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .breadcrumb-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        /* JIRA Fields Display Styles */
        .jira-field-display {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 1rem;
            border: 2px solid rgba(245, 158, 11, 0.2);
            transition: all 0.3s ease;
        }

        .jira-field-display:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.4);
        }

        .jira-field-label {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #374151;
            font-size: 0.875rem;
        }

        .jira-field-value {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            min-height: 1.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 1px solid rgba(209, 213, 219, 0.5);
        }

        .jira-field-value.empty {
            color: #9ca3af;
            font-style: italic;
            font-weight: 400;
        }

        @keyframes shimmer {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
            }
        }

        @keyframes completedBounce {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .step-circle.completed-animated {
            animation: completedBounce 0.6s ease-out;
        }

        .step-item.active .step-circle {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }

        .step-item.completed .step-circle {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
        }

        .step-item.completed .step-circle::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* Language Dropdown */
        .language-dropdown {
            background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
            border: 2px solid #f59e0b;
        }

        /* Progress Bars */
        .upload-progress {
            width: 100%;
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .upload-progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        /* CRITICAL: Processing Progress Styles */
        .processing-progress-container {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 1.5rem;
            padding: 2rem;
            margin: 2rem 0;
            display: none;
        }

        .processing-progress-container.active {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-progress {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step-item:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 60%;
            right: -40%;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .step-item.completed::after {
            background: #10b981;
        }

        .step-item.active::after {
            background: linear-gradient(90deg, #10b981 0%, #e2e8f0 100%);
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #64748b;
            z-index: 2;
            position: relative;
            transition: all 0.3s ease;
        }

        .step-item.completed .step-circle {
            background: #10b981;
            color: white;
        }

        .step-item.active .step-circle {
            background: #0ea5e9;
            color: white;
            animation: pulse 2s infinite;
        }

        .step-label {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            color: #64748b;
        }

        .step-item.completed .step-label {
            color: #10b981;
        }

        .step-item.active .step-label {
            color: #0ea5e9;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
            }
        }

        .progress-main {
            height: 12px;
            background: #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-main .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 6px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .progress-main .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shine 2s infinite;
        }

        .progress-main .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            border-radius: 6px;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .status-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #0ea5e9;
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .progress-percentage {
            font-size: 2rem;
            font-weight: 900;
            color: #10b981;
            text-align: center;
            margin-bottom: 1rem;
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-notification {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            min-width: 300px;
            border-left: 4px solid #10b981;
            animation: slideInRight 0.3s ease;
        }

        .toast-notification.error {
            border-left-color: #ef4444;
        }

        .toast-notification.warning {
            border-left-color: #f59e0b;
        }

        .toast-notification.info {
            border-left-color: #0ea5e9;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Reject Modal */
        .reject-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            backdrop-filter: blur(8px);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }

        .reject-modal.active {
            display: flex;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .reject-modal-content {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.25);
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .enhanced-container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .kpi-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }

            .kpi-card {
                padding: 1.5rem;
                min-height: 140px;
            }

            .kpi-number {
                font-size: 2rem;
            }

            .enhanced-table th,
            .enhanced-table td {
                padding: 1rem;
            }

            .table-header,
            .table-filters {
                padding: 1.5rem;
            }

            .modal .modal-dialog {
                width: 98%;
                max-height: 90vh;
            }

            .modal .modal-content {
                height: 90vh;
            }

            .step-progress {
                flex-wrap: wrap;
            }

            .step-item {
                flex-basis: calc(33.333% - 1rem);
                margin-bottom: 1rem;
            }

            .step-item::after {
                display: none;
            }
        }
		/* 🚀 SPECTACULAR PROCESSING PROGRESS STYLES 🚀 */
		.processing-progress-container {
			background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 25%, #2d1b69 50%, #1e293b 75%, #0f172a 100%);
			border: 3px solid transparent;
			background-clip: padding-box;
			border-radius: 2rem;
			padding: 3rem 2rem;
			margin: 2rem 0;
			display: none;
			position: relative;
			overflow: hidden;
			box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1);
		}

		.processing-progress-container::before {
			content: '';
			position: absolute;
			top: -2px;
			left: -2px;
			right: -2px;
			bottom: -2px;
			background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b, #10b981, #3b82f6);
			background-size: 400% 400%;
			border-radius: 2rem;
			z-index: -1;
			animation: borderGlow 4s ease-in-out infinite;
		}

		.processing-progress-container::after {
			content: '';
			position: absolute;
			top: -50%;
			left: -50%;
			width: 200%;
			height: 200%;
			background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
			animation: backgroundShine 3s linear infinite;
			z-index: 0;
		}

		.processing-progress-container.active {
			display: block;
			animation: processContainerSlideIn 1s ease-out;
		}

		@keyframes processContainerSlideIn {
			from {
				opacity: 0;
				transform: translateY(-50px) scale(0.9);
				filter: blur(10px);
			}
			to {
				opacity: 1;
				transform: translateY(0) scale(1);
				filter: blur(0);
			}
		}

		@keyframes borderGlow {
			0%, 100% { background-position: 0% 50%; }
			50% { background-position: 100% 50%; }
		}

		@keyframes backgroundShine {
			0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
			100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
		}

		/* 🎯 PROCESSING HEADER */
		.processing-header {
			text-align: center;
			margin-bottom: 3rem;
			position: relative;
			z-index: 2;
		}

		.processing-icon-container {
			position: relative;
			display: inline-block;
			width: 100px;
			height: 100px;
		}

		.processing-icon {
			font-size: 4rem;
			color: #3b82f6;
			animation: processingRotate 3s linear infinite;
			position: relative;
			z-index: 3;
			filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
		}

		@keyframes processingRotate {
			from { transform: rotate(0deg); }
			to { transform: rotate(360deg); }
		}

		.processing-pulse-ring {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 120px;
			height: 120px;
			border: 3px solid #3b82f6;
			border-radius: 50%;
			transform: translate(-50%, -50%);
			animation: processingPulse 2s ease-in-out infinite;
		}

		.processing-pulse-ring-2 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 150px;
			height: 150px;
			border: 2px solid rgba(139, 92, 246, 0.6);
			border-radius: 50%;
			transform: translate(-50%, -50%);
			animation: processingPulse 2s ease-in-out infinite 0.7s;
		}

		.processing-pulse-ring-3 {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 180px;
			height: 180px;
			border: 1px solid rgba(236, 72, 153, 0.4);
			border-radius: 50%;
			transform: translate(-50%, -50%);
			animation: processingPulse 2s ease-in-out infinite 1.4s;
		}

		@keyframes processingPulse {
			0%, 100% {
				transform: translate(-50%, -50%) scale(1);
				opacity: 1;
			}
			50% {
				transform: translate(-50%, -50%) scale(1.3);
				opacity: 0.3;
			}
		}

		.processing-title {
			color: #f8fafc;
			font-size: 2.5rem;
			font-weight: 900;
			margin: 1.5rem 0 0.5rem 0;
			text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
			background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		.processing-subtitle {
			color: #cbd5e1;
			font-size: 1.2rem;
			margin: 0 0 1rem 0;
			opacity: 0.9;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
		}

		.processing-stats {
			display: flex;
			justify-content: center;
			gap: 2rem;
			margin-top: 1rem;
		}

		.stat-item {
			display: flex;
			align-items: center;
			gap: 0.5rem;
			color: #94a3b8;
			font-size: 0.9rem;
			font-weight: 600;
		}

		.stat-item i {
			color: #3b82f6;
		}

		/* Spectacular Progress Visualization */
		.progress-visualization-container {
			position: relative;
			margin: 3rem 0;
			z-index: 2;
		}

		.progress-main-enhanced {
			height: 20px;
			background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
			border-radius: 10px;
			overflow: hidden;
			position: relative;
			border: 1px solid #475569;
			box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
		}

		.progress-bar-enhanced {
			height: 100%;
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
			border-radius: 10px;
			position: relative;
			transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
			box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
		}

		.progress-shine {
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
			animation: progressShine 2s linear infinite;
		}

		@keyframes progressShine {
			0% { left: -100%; }
			100% { left: 100%; }
		}

		.progress-particles {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-image: 
				radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.6) 2px, transparent 2px),
				radial-gradient(circle at 60% 30%, rgba(255, 255, 255, 0.4) 1px, transparent 1px),
				radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.5) 1.5px, transparent 1.5px);
			animation: particlesFloat 3s linear infinite;
		}

		@keyframes particlesFloat {
			0% { transform: translateX(0); opacity: 0.8; }
			100% { transform: translateX(20px); opacity: 0.2; }
		}

		.progress-percentage-overlay {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 0.9rem;
			font-weight: 900;
			color: #ffffff;
			text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
			z-index: 3;
		}

		/* Current Step Indicator */
		.current-step-indicator {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			border-radius: 1rem;
			padding: 1rem 2rem;
			text-align: center;
			position: relative;
			overflow: hidden;
			margin: 2rem 0;
			box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
		}

		.step-glow-effect {
			position: absolute;
			top: -50%;
			left: -50%;
			width: 200%;
			height: 200%;
			background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
			animation: glowPulse 2s ease-in-out infinite alternate;
		}

		@keyframes glowPulse {
			0% { opacity: 0.3; transform: scale(1); }
			100% { opacity: 0.7; transform: scale(1.1); }
		}

		#currentStepText {
			font-size: 1.2rem;
			font-weight: 700;
			color: #ffffff;
			position: relative;
			z-index: 2;
			text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
		}

		/* Enhanced 14-Step Progress */
		.step-progress-spectacular {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
			gap: 1rem;
			margin: 3rem 0;
			position: relative;
			z-index: 2;
		}

		.step-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center;
			position: relative;
			transition: all 0.3s ease;
		}

		.step-circle {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
			border: 2px solid #6b7280;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 900;
			font-size: 1.1rem;
			color: #9ca3af;
			position: relative;
			transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
		}

		.step-label {
			margin-top: 0.75rem;
			font-size: 0.85rem;
			font-weight: 700;
			color: #9ca3af;
			transition: all 0.3s ease;
		}

		.step-description {
			margin-top: 0.25rem;
			font-size: 0.7rem;
			color: #6b7280;
			line-height: 1.2;
		}

		/* Active Step */
		.step-item.active .step-circle {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			border-color: #3b82f6;
			color: #ffffff;
			animation: activeStepPulse 2s infinite;
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
		}

		.step-item.active .step-label,
		.step-item.active .step-description {
			color: #3b82f6;
		}

		@keyframes activeStepPulse {
			0%, 100% { box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4); }
			50% { box-shadow: 0 8px 35px rgba(59, 130, 246, 0.6); }
		}

		/* Completed Step */
		.step-item.completed .step-circle {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			border-color: #10b981;
			color: #ffffff;
			transform: scale(1.05);
			box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
		}

		.step-item.completed .step-circle::after {
			content: '✓';
			position: absolute;
			font-size: 1.2rem;
			font-weight: 900;
			color: #ffffff;
		}

		.step-item.completed .step-label,
		.step-item.completed .step-description {
			color: #10b981;
		}

		/* 🌟 ENHANCED PROGRESS VISUALIZATION */
		.progress-visualization-container {
			position: relative;
			margin: 3rem 0;
			z-index: 2;
		}

		.progress-track {
			height: 24px;
			background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
			border-radius: 12px;
			overflow: hidden;
			box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4);
			position: relative;
		}

		.progress-wave {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
			animation: waveMove 3s ease-in-out infinite;
		}

		@keyframes waveMove {
			0%, 100% { transform: translateX(-100%); }
			50% { transform: translateX(100%); }
		}

		/* ✨ CURRENT STEP INDICATOR WITH GLOW EFFECT */
		.current-step-indicator {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			border-radius: 1.5rem;
			padding: 1.5rem 2rem;
			text-align: center;
			position: relative;
			overflow: hidden;
			margin: 2rem 0;
			box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 1rem;
		}

		.step-icon-container {
			position: relative;
			width: 50px;
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.step-icon {
			font-size: 2rem;
			color: #ffffff;
			filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
			animation: stepIconPulse 2s ease-in-out infinite;
		}

		@keyframes stepIconPulse {
			0%, 100% { transform: scale(1); }
			50% { transform: scale(1.1); }
		}

		#currentStepText {
			font-size: 1.3rem;
			font-weight: 700;
			color: #ffffff;
			position: relative;
			z-index: 2;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			transition: all 0.3s ease;
		}

		/* 🎨 ENHANCED STEP PROGRESS BARS */
		.step-progress-bar {
			position: absolute;
			bottom: 0;
			left: 0;
			height: 4px;
			width: 0%;
			background: linear-gradient(90deg, #3b82f6, #8b5cf6);
			transition: width 0.8s ease;
			border-radius: 0 0 1.5rem 1.5rem;
		}

		.step-number {
			position: relative;
			z-index: 2;
			transition: all 0.3s ease;
		}

		.step-check {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%) scale(0);
			font-size: 1.5rem;
			color: #ffffff;
			transition: all 0.3s ease;
			z-index: 2;
		}

		/* 💻 SPECTACULAR LIVE CONSOLE */
		.live-console-container {
			background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
			border: 2px solid #374151;
			border-radius: 1.5rem;
			margin-top: 3rem;
			overflow: hidden;
			box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
			position: relative;
			z-index: 2;
		}

		.console-header {
			background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
			padding: 1rem 1.5rem;
			border-bottom: 1px solid #4b5563;
		}

		.console-indicator {
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: #10b981;
			margin-right: 0.75rem;
			animation: consoleBlink 2s infinite;
		}

		@keyframes consoleBlink {
			0%, 50% { opacity: 1; }
			51%, 100% { opacity: 0.3; }
		}

		.console-title {
			font-size: 1rem;
			font-weight: 700;
			color: #ffffff;
			margin: 0;
		}

		.console-controls {
			display: flex;
			gap: 0.5rem;
		}

		.console-btn {
			background: transparent;
			border: 1px solid #6b7280;
			color: #9ca3af;
			border-radius: 0.5rem;
			padding: 0.5rem 0.75rem;
			font-size: 0.8rem;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.console-btn:hover {
			background: rgba(59, 130, 246, 0.1);
			border-color: #3b82f6;
			color: #3b82f6;
			transform: translateY(-1px);
		}

		.console-status {
			background: linear-gradient(135deg, #10b981, #059669);
			color: #ffffff;
			padding: 0.25rem 0.75rem;
			border-radius: 1rem;
			font-size: 0.8rem;
			font-weight: 600;
			margin-left: 1rem;
			animation: statusPulse 2s ease-in-out infinite;
		}

		@keyframes statusPulse {
			0%, 100% { opacity: 1; }
			50% { opacity: 0.7; }
		}

		.console-output {
			height: 300px;
			overflow-y: auto;
			padding: 1rem 1.5rem;
			font-family: 'Courier New', monospace;
			font-size: 0.85rem;
			line-height: 1.4;
			background: #0f172a;
			scrollbar-width: thin;
			scrollbar-color: #4b5563 #1e293b;
		}

		.console-output::-webkit-scrollbar {
			width: 8px;
		}

		.console-output::-webkit-scrollbar-track {
			background: #1e293b;
		}

		.console-output::-webkit-scrollbar-thumb {
			background: #4b5563;
			border-radius: 4px;
		}

		.console-line {
			margin-bottom: 0.5rem;
			animation: consoleLineSlide 0.3s ease-out;
		}

		@keyframes consoleLineSlide {
			from { opacity: 0; transform: translateX(-10px); }
			to { opacity: 1; transform: translateX(0); }
		}

		.console-timestamp {
			color: #6b7280;
			margin-right: 0.75rem;
		}

		.console-message {
			color: #e2e8f0;
		}

		.console-line.success .console-message { color: #10b981; }
		.console-line.error .console-message { color: #ef4444; }
		.console-line.warning .console-message { color: #f59e0b; }
		.console-line.info .console-message { color: #3b82f6; }
		.console-line.startup .console-message { color: #8b5cf6; }

		/* Responsive Design */
		@media (max-width: 768px) {
			.step-progress-spectacular {
				grid-template-columns: repeat(3, 1fr);
				gap: 0.75rem;
			}
			
			.step-circle {
				width: 50px;
				height: 50px;
				font-size: 1rem;
			}
			
			.console-output {
				height: 200px;
				font-size: 0.75rem;
			}
			
			.processing-icon {
				font-size: 2rem;
			}
			
			.processing-title {
				font-size: 1.5rem;
			}
		}

    </style>
</head>
<body>
    <!-- Enhanced Header -->
    <header class="gradient-header">
        <div class="enhanced-container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="icon-container me-4">
                        <i class="fas fa-video"></i>
                    </div>
                    <div>
                        <h1 class="text-white mb-2 fw-bold" style="font-size: 2.5rem;">Archives TrimsFlow</h1>
                        <p class="text-white-50 mb-0 fs-5 fw-medium">Professional Trim Backup Processing System</p>
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <!-- Status Indicator -->
                    <div class="d-flex align-items-center me-4">
                        <div class="status-indicator me-3"></div>
                        <span class="text-white fs-6 fw-medium">System Online</span>
                    </div>

                    <!-- User Info -->
                    <div class="d-flex align-items-center me-4">
                        <div class="text-end me-3">
                            <div class="text-white fw-bold fs-4" id="userFullName">{{ full_name or username }}</div>
                            <div class="text-white-50" id="userRole">{{ role.title() }} User</div>
                        </div>
                        <div class="icon-container" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="d-flex align-items-center">
                        {% if role == 'admin' %}
                        <a href="/admin" class="text-white text-decoration-none me-4 fw-semibold d-flex align-items-center" id="adminLink">
                            <i class="fas fa-cog me-2"></i> Admin Panel
                        </a>
                        {% endif %}
                        <a href="/logout" class="text-white text-decoration-none fw-semibold d-flex align-items-center">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="enhanced-container main-content">
        <!-- KPI Cards Section -->
        <section class="mb-4">
            <div class="kpi-grid">
                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);">
                    <div class="kpi-label">Trim Backup Queue</div>
                    <div class="kpi-number" id="pending-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <span>Pending Processing</span>
                    </div>
                </div>

                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <div class="kpi-label">Done Today</div>
                    <div class="kpi-number" id="done-today-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <span>Completed Today</span>
                    </div>
                </div>

                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                    <div class="kpi-label">Rejected Today</div>
                    <div class="kpi-number" id="rejected-today-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-times-circle me-2"></i>
                        <span>Requires Attention</span>
                    </div>
                </div>

                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                    <div class="kpi-label">Yesterday</div>
                    <div class="kpi-number" id="done-yesterday-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-day me-2"></i>
                        <span>Previous Day</span>
                    </div>
                </div>

                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <div class="kpi-label">This Week</div>
                    <div class="kpi-number" id="done-week-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-week me-2"></i>
                        <span>Weekly Total</span>
                    </div>
                </div>

                <div class="kpi-card text-white" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                    <div class="kpi-label">This Month</div>
                    <div class="kpi-number" id="done-month-count">-</div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <span>Monthly Progress</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Issues Table Section -->
        <section>
            <div class="enhanced-table-container">
                <!-- Table Header -->
                <div class="table-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold text-gray-900 mb-3 d-flex align-items-center" style="font-size: 2rem;">
                                <i class="fas fa-list-ul text-primary me-3"></i>
                                Trim Backup Issues Queue
                            </h2>
                            <p class="text-muted mb-0 fs-5">Issues with status "Trim Backup Done" from 2023-02-01 onwards</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <button type="button" onclick="refreshQueue(); return false;" class="enhanced-btn enhanced-btn-lg btn-primary">
                                <i class="fas fa-sync-alt"></i>Refresh Queue
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Section -->
                <div class="table-filters">
                    <div class="enhanced-grid enhanced-grid-3">
                        <div class="form-field-group">
                            <label class="form-label">
                                <i class="fas fa-search text-primary"></i>
                                Search Tickets
                            </label>
                            <input type="text" id="searchInput" placeholder="Search by ID, title, or assignee..." class="enhanced-input">
                        </div>
                        <div class="form-field-group">
                            <label class="form-label">
                                <i class="fas fa-filter text-info"></i>
                                Filter by Status
                            </label>
                            <select id="statusFilter" class="enhanced-select">
                                <option value="">All Status</option>
                                <option value="Trim Backup Done">Trim Backup Done</option>
                            </select>
                        </div>
                        <div class="form-field-group">
                            <label class="form-label">
                                <i class="fas fa-user-tag text-success"></i>
                                Filter by Assignee
                            </label>
                            <select id="assigneeFilter" class="enhanced-select">
                                <option value="">All Project Assignees</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Issues Table -->
                <div class="table-responsive">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-ticket-alt me-2"></i>
                                    Issue Details
                                </th>
                                <th>
                                    <i class="fas fa-user-edit me-2"></i>
                                    Project Assignee
                                </th>
                                <th>
                                    <i class="fas fa-flag me-2"></i>
                                    Status
                                </th>
                                <th>
                                    <i class="fas fa-link me-2"></i>
                                    OCD Information
                                </th>
                                <th>
                                    <i class="fas fa-cogs me-2"></i>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="issues-tbody">
                            <!-- Dynamic content will be populated here -->
                        </tbody>
                    </table>
                </div>

                <!-- Loading State -->
                <div id="loadingState" class="d-none text-center py-5">
                    <div class="d-inline-flex align-items-center">
                        <div class="spinner-border text-primary me-4" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div>
                            <div class="fs-4 fw-bold text-primary mb-2">Loading Trim Backup Issues...</div>
                            <div class="text-muted">Fetching data from JIRA servers</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Reject Modal -->
    <div id="rejectModal" class="reject-modal">
        <div class="reject-modal-content">
            <div class="p-4 border-bottom d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="fw-bold text-gray-900 mb-2 d-flex align-items-center" style="font-size: 1.5rem;">
                        <i class="fas fa-times-circle text-danger me-3"></i>
                        Reject Issue
                    </h3>
                    <p class="text-muted mb-0">Please provide a detailed reason for rejection</p>
                </div>
                <button type="button" onclick="closeRejectModal(); return false;" class="btn-close"></button>
            </div>
            <div class="p-4">
                <div class="form-field-group">
                    <label for="rejectReason" class="form-label">
                        <i class="fas fa-comment-alt text-danger"></i>
                        Reason for rejection *
                    </label>
                    <textarea id="rejectReason" rows="4"
                              class="enhanced-textarea"
                              placeholder="Please provide a detailed reason for rejection..."></textarea>
                </div>
            </div>
            <div class="p-4 border-top d-flex justify-content-end gap-3">
                <button type="button" onclick="closeRejectModal(); return false;"
                        class="enhanced-btn btn-light text-dark">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" onclick="confirmReject(); return false;"
                        class="enhanced-btn btn-danger">
                    <i class="fas fa-times-circle me-2"></i>Reject Issue
                </button>
            </div>
        </div>
    </div>

    <!-- Accept & Proceed Modal with Fixed Scrolling -->
    <div class="modal fade" id="acceptModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <div>
                        <h3 class="fw-bold text-white mb-2 d-flex align-items-center" style="font-size: 1.75rem;">
                            <i class="fas fa-cogs text-white me-3"></i>
                            Accept & Proceed
                        </h3>
                        <p class="text-white mb-0 opacity-75">Process video content with intelligent auto-population</p>
                    </div>
                    <button type="button" class="btn-close btn-close-white" onclick="closeAcceptModal(); return false;"></button>
                </div>

                <div class="modal-body">
                    <form id="acceptForm" onsubmit="return false;">
                        <!-- Processing Progress Container -->
                        <!-- 🚀 SPECTACULAR PROCESSING PROGRESS CONTAINER 🚀 -->
                        <div id="processingProgressContainer" class="processing-progress-container">
                            <!-- 🎯 MAIN PROCESSING HEADER WITH LIVE ANIMATION -->
                            <div class="processing-header">
                                <div class="d-flex align-items-center justify-content-center mb-4">
                                    <div class="processing-icon-container">
                                        <i class="fas fa-cogs processing-icon"></i>
                                        <div class="processing-pulse-ring"></div>
                                        <div class="processing-pulse-ring-2"></div>
                                        <div class="processing-pulse-ring-3"></div>
                                    </div>
                                    <div class="ms-4">
                                        <h2 class="processing-title">🎬 Archives TrimsFlow Processing</h2>
                                        <p class="processing-subtitle" id="processingStatusText">Initializing comprehensive video processing pipeline...</p>
                                        <div class="processing-stats">
                                            <span class="stat-item">
                                                <i class="fas fa-clock"></i>
                                                <span id="processingTime">00:00</span>
                                            </span>
                                            <span class="stat-item">
                                                <i class="fas fa-tasks"></i>
                                                <span id="currentStepCounter">0/14</span>
                                            </span>
                                            <span class="stat-item">
                                                <i class="fas fa-tachometer-alt"></i>
                                                <span id="processingSpeed">Initializing...</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 🌟 SPECTACULAR PROGRESS VISUALIZATION -->
                            <div class="progress-visualization-container">
                                <div class="progress-main-enhanced mb-4">
                                    <div class="progress-track">
                                        <div class="progress-bar-enhanced" id="processingProgressBar" style="width: 0%">
                                            <div class="progress-shine"></div>
                                            <div class="progress-particles"></div>
                                            <div class="progress-wave"></div>
                                        </div>
                                    </div>
                                    <div class="progress-percentage-overlay">
                                        <span id="processingPercentage">0</span>%
                                    </div>
                                </div>

                                <!-- ✨ CURRENT STEP INDICATOR WITH GLOW EFFECT -->
                                <div class="current-step-indicator">
                                    <div class="step-glow-effect"></div>
                                    <div class="step-icon-container">
                                        <i class="fas fa-play-circle step-icon" id="currentStepIcon"></i>
                                    </div>
                                    <span id="currentStepText">Preparing to start processing...</span>
                                </div>
                            </div>

                            <!-- 🎨 ENHANCED 14-STEP PROGRESS WITH SPECTACULAR ANIMATION -->
                            <div class="step-progress-spectacular" id="stepProgress">
                                <div class="step-item" data-step="1">
                                    <div class="step-circle">
                                        <span class="step-number">1</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Google Sheets</div>
                                    <div class="step-description">Save to database</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="2">
                                    <div class="step-circle">
                                        <span class="step-number">2</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Copy Folder</div>
                                    <div class="step-description">Working area transfer</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="3">
                                    <div class="step-circle">
                                        <span class="step-number">3</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Rename Folder</div>
                                    <div class="step-description">Apply naming convention</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="4">
                                    <div class="step-circle">
                                        <span class="step-number">4</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Output Folder</div>
                                    <div class="step-description">Normalize structure</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="5">
                                    <div class="step-circle">
                                        <span class="step-number">5</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Video File</div>
                                    <div class="step-description">Rename to OCD format</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="6">
                                    <div class="step-circle">
                                        <span class="step-number">6</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Stems Folder</div>
                                    <div class="step-description">Normalize stems</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="7">
                                    <div class="step-circle">
                                        <span class="step-number">7</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Stems Files</div>
                                    <div class="step-description">Rename stem files</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="8">
                                    <div class="step-circle">
                                        <span class="step-number">8</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">ILP/GLP</div>
                                    <div class="step-description">Process media files</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="9">
                                    <div class="step-circle">
                                        <span class="step-number">9</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">SRT Copy</div>
                                    <div class="step-description">Copy subtitles</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="10">
                                    <div class="step-circle">
                                        <span class="step-number">10</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Validation</div>
                                    <div class="step-description">Verify integrity</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="11">
                                    <div class="step-circle">
                                        <span class="step-number">11</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Audio Extract</div>
                                    <div class="step-description">Generate audio files</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="12">
                                    <div class="step-circle">
                                        <span class="step-number">12</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Drive Upload</div>
                                    <div class="step-description">Upload to cloud</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="13">
                                    <div class="step-circle">
                                        <span class="step-number">13</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Update Jira</div>
                                    <div class="step-description">Link Google Drive</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                                <div class="step-item" data-step="14">
                                    <div class="step-circle">
                                        <span class="step-number">14</span>
                                        <i class="fas fa-check step-check"></i>
                                    </div>
                                    <div class="step-label">Close Ticket</div>
                                    <div class="step-description">Complete processing</div>
                                    <div class="step-progress-bar"></div>
                                </div>
                            </div>

                            <!-- 💻 SPECTACULAR LIVE CONSOLE OUTPUT -->
                            <div class="live-console-container">
                                <div class="console-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="console-indicator"></div>
                                            <h5 class="console-title">
                                                <i class="fas fa-terminal me-2"></i>
                                                Live Processing Console
                                            </h5>
                                            <div class="console-status" id="consoleStatus">ACTIVE</div>
                                        </div>
                                        <div class="console-controls">
                                            <button class="console-btn" onclick="toggleConsoleAutoScroll()" title="Toggle Auto-scroll" id="autoScrollBtn">
                                                <i class="fas fa-arrows-alt-v"></i>
                                            </button>
                                            <button class="console-btn" onclick="clearConsole()" title="Clear Console">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="console-btn" onclick="scrollToBottomConsole()" title="Scroll to Bottom">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="console-output" id="consoleOutput">
                                    <div class="console-line startup">
                                        <span class="console-timestamp">[SYSTEM]</span>
                                        <span class="console-message">🎬 Archives TrimsFlow processing system initialized...</span>
                                    </div>
                                    <div class="console-line info">
                                        <span class="console-timestamp">[INFO]</span>
                                        <span class="console-message">Ready to begin comprehensive video processing pipeline</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Folder Browser Section -->
                        <div class="mb-4" id="folderBrowserSection">
                            <div class="form-field-group" style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-color: #0ea5e9;">
                                <h4 class="fw-bold text-gray-900 mb-3 d-flex align-items-center">
                                    <i class="fas fa-folder-open text-primary me-3"></i>
                                    Server Directory Browser
                                </h4>

                                <div class="enhanced-grid enhanced-grid-2">
                                    <!-- Selected Path Display -->
                                    <div>
                                        <label for="sourceFolder" class="form-label">
                                            <i class="fas fa-map-marker-alt text-primary"></i>
                                            Selected Folder Path
                                        </label>
                                        <input type="text" id="sourceFolder" readonly
                                               class="enhanced-input font-monospace fw-bold"
                                               placeholder="Browse and select a folder from the server">

                                        <!-- Folder Info Panel -->
                                        <div id="folderInfo" class="d-none mt-3 p-3 rounded-3" style="background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%); border: 2px solid #10b981;">
                                            <h5 class="fw-bold text-gray-800 mb-2 d-flex align-items-center">
                                                <i class="fas fa-info-circle text-success me-2"></i>
                                                Folder Information
                                            </h5>
                                            <div class="d-flex justify-content-between">
                                                <span class="text-muted fw-semibold">Path:</span>
                                                <span id="folderPath" class="font-monospace text-primary text-break"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Browser Panel -->
                                    <div>
                                        <label class="form-label">
                                            <i class="fas fa-server text-primary"></i>
                                            Browse Directories
                                        </label>
                                        <div class="bg-white rounded-3 border-2 border-gray-200 overflow-hidden">
                                            <!-- Breadcrumb -->
                                            <div id="breadcrumb" class="breadcrumb-container border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-server text-primary me-2"></i>
                                                    <span class="text-muted">Loading...</span>
                                                </div>
                                            </div>

                                            <!-- Browser Content -->
                                            <div id="folderBrowser" class="folder-browser">
                                                <div class="p-4 text-center text-muted">
                                                    <div class="loading-shimmer w-100 mb-3 rounded" style="height: 1.5rem;"></div>
                                                    <div class="loading-shimmer w-75 mb-3 rounded" style="height: 1.5rem;"></div>
                                                    <div class="fs-6 fw-medium">Loading directories...</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-populated Fields Section -->
                        <div class="mb-4" id="autoFieldsSection">
                            <h4 class="fw-bold text-gray-900 mb-3 d-flex align-items-center">
                                <i class="fas fa-magic text-success me-3"></i>
                                Auto-Populated Fields
                            </h4>

                            <div class="enhanced-grid enhanced-grid-3">
                                <!-- Editor Field -->
                                <div class="form-field-group auto-field">
                                    <label for="editor" class="form-label">
                                        <i class="fas fa-user-edit text-success"></i>
                                        Editor
                                    </label>
                                    <input type="text" id="editor" class="enhanced-input">
                                </div>

                                <!-- OCD Number Field -->
                                <div class="form-field-group auto-field">
                                    <label for="ocdNumber" class="form-label">
                                        <i class="fas fa-link text-primary"></i>
                                        OCD Number
                                    </label>
                                    <input type="text" id="ocdNumber" class="enhanced-input">
                                </div>

                                <!-- Audio Code Field -->
                                <div class="form-field-group">
                                    <label for="audioCode" class="form-label">
                                        <i class="fas fa-music text-purple"></i>
                                        Audio Code
                                    </label>
                                    <div class="input-group">
                                        <input type="text" id="audioCode" readonly class="enhanced-input" style="border-top-right-radius: 0; border-bottom-right-radius: 0;">
                                        <button type="button" onclick="fetchAudioCode(); return false;" class="enhanced-btn btn-info" style="border-top-left-radius: 0; border-bottom-left-radius: 0; margin: 0;">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Title Field -->
                                <div class="form-field-group auto-field">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading text-warning"></i>
                                        Title
                                    </label>
                                    <input type="text" id="title" class="enhanced-input">
                                </div>

                                <!-- CRITICAL: Duration Field with File Upload -->
                                <div class="form-field-group file-upload-section">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock text-danger"></i>
                                        Duration Detection
                                    </label>

                                    <!-- File Upload Zone -->
                                    <div class="file-drop-zone" id="fileDropZone" onclick="document.getElementById('durationFileInput').click();">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-cloud-upload-alt text-warning fs-2 mb-2"></i>
                                            <div class="fw-bold text-gray-700 mb-1">Click to Browse Video/Audio File</div>
                                            <div class="text-muted small">Or drag and drop files here</div>
                                            <div class="text-muted small mt-1">Supports: MOV, MP4, AVI, MKV, WAV, MP3, AAC</div>
                                        </div>
                                    </div>

                                    <input type="file" id="durationFileInput" class="d-none"
                                           accept="video/*,audio/*,.mov,.mp4,.avi,.mkv,.wmv,.wav,.mp3,.aac,.m4a">

                                    <!-- File Info Display -->
                                    <div id="fileInfo" class="file-info">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <div class="fw-bold text-gray-800">
                                                    <i class="fas fa-file-video me-2"></i>
                                                    <span id="fileName">Selected File</span>
                                                </div>
                                                <div class="text-muted small">
                                                    Size: <span id="fileSize">-</span> |
                                                    Type: <span id="fileType">-</span>
                                                </div>
                                            </div>
                                            <button type="button" class="enhanced-btn btn-success" onclick="uploadAndDetectDuration()">
                                                <i class="fas fa-search me-2"></i>Detect Duration
                                            </button>
                                        </div>

                                        <!-- Upload Progress -->
                                        <div id="uploadProgress" class="upload-progress d-none">
                                            <div id="uploadProgressBar" class="upload-progress-bar" style="width: 0%;"></div>
                                        </div>
                                    </div>

                                    <!-- Duration Result Display -->
                                    <div class="input-group mt-3">
                                        <input type="text" id="duration" class="enhanced-input" readonly
                                               placeholder="Duration will appear here..."
                                               style="border-top-right-radius: 0; border-bottom-right-radius: 0;">
                                        <button type="button" onclick="detectDurationFromFolder(); return false;"
                                                class="enhanced-btn btn-primary"
                                                style="border-top-left-radius: 0; border-bottom-left-radius: 0; margin: 0;"
                                                title="Detect from selected folder">
                                            <i class="fas fa-folder-open"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Backup Type Field -->
                                <div class="form-field-group auto-field">
                                    <label for="backupType" class="form-label">
                                        <i class="fas fa-archive text-info"></i>
                                        Backup Type
                                    </label>
                                    <input type="text" id="backupType" class="enhanced-input">
                                </div>
                            </div>
                        </div>

                        <!-- JIRA Fields Display Section -->
                        <div class="mb-4" id="jiraFieldsSection" style="display: none;">
                            <div class="form-field-group" style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-color: #f59e0b;">
                                <h4 class="fw-bold text-gray-900 mb-3 d-flex align-items-center">
                                    <i class="fab fa-jira text-warning me-3"></i>
                                    JIRA Information
                                </h4>

                                <div class="enhanced-grid enhanced-grid-2">
                                    <!-- Vertical Field -->
                                    <div class="jira-field-display">
                                        <div class="jira-field-label">
                                            <i class="fas fa-building text-primary me-2"></i>
                                            <span class="fw-semibold">Dept / Vertical</span>
                                        </div>
                                        <div class="jira-field-value" id="jiraVertical">
                                            <div class="loading-shimmer rounded" style="height: 1.2rem; width: 80%;"></div>
                                        </div>
                                    </div>

                                    <!-- VP Audience Type Field -->
                                    <div class="jira-field-display">
                                        <div class="jira-field-label">
                                            <i class="fas fa-users text-success me-2"></i>
                                            <span class="fw-semibold">VP Audience Type</span>
                                        </div>
                                        <div class="jira-field-value" id="jiraVpAudienceType">
                                            <div class="loading-shimmer rounded" style="height: 1.2rem; width: 70%;"></div>
                                        </div>
                                    </div>

                                    <!-- VP Trimbackup Folder Name Field -->
                                    <div class="jira-field-display">
                                        <div class="jira-field-label">
                                            <i class="fas fa-folder text-info me-2"></i>
                                            <span class="fw-semibold">VP Trimbackup Folder Name</span>
                                        </div>
                                        <div class="jira-field-value" id="jiraVpTrimbackupFolderName">
                                            <div class="loading-shimmer rounded" style="height: 1.2rem; width: 90%;"></div>
                                        </div>
                                    </div>

                                    <!-- Social Media Platform Field -->
                                    <div class="jira-field-display">
                                        <div class="jira-field-label">
                                            <i class="fas fa-share-alt text-purple me-2"></i>
                                            <span class="fw-semibold">Social Media Platform</span>
                                        </div>
                                        <div class="jira-field-value" id="jiraSocialMediaPlatform">
                                            <div class="loading-shimmer rounded" style="height: 1.2rem; width: 75%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Processing Configuration Section -->
                        <div class="mb-4" id="configSection">
                            <h4 class="fw-bold text-gray-900 mb-3 d-flex align-items-center">
                                <i class="fas fa-sliders-h text-secondary me-3"></i>
                                Processing Configuration
                            </h4>

                            <div class="enhanced-grid enhanced-grid-5">
                                <!-- Type of Video -->
                                <div class="form-field-group">
                                    <label for="videoType" class="form-label">
                                        <i class="fas fa-video text-primary"></i>
                                        Video Type
                                    </label>
                                    <select id="videoType" class="enhanced-select">
                                        <option value="">Select type...</option>
                                        <option value="Talk">Talk</option>
                                        <option value="Documentary">Documentary</option>
                                        <option value="Insta-Reels">Insta-Reels</option>
                                        <option value="Class">Class</option>
                                        <option value="Interview">Interview</option>
                                        <option value="Promo">Promo</option>
                                        <option value="Daily-Mystic-Quote">Daily-Mystic-Quote</option>
                                        <option value="Episode">Episode</option>
                                        <option value="Q-And-A">Q-And-A</option>
                                        <option value="Message">Message</option>
                                        <option value="Intro">Intro</option>
                                        <option value="Song">Song</option>
                                        <option value="Glimpses">Glimpses</option>
                                        <option value="Animation">Animation</option>
                                        <option value="Sharings">Sharings</option>
                                        <option value="Video-Book">Video-Book</option>
                                        <option value="Teaser">Teaser</option>
                                        <option value="Poem">Poem</option>
                                        <option value="Telefilm">Telefilm</option>
                                        <option value="Non-Ashram Videos">Non-Ashram Videos</option>
                                        <option value="Event">Event</option>
                                        <option value="Miscellaneous">Miscellaneous</option>
										<option value="Miracle-Of-Mind">Miracle-Of-Mind</option>
                                    </select>
                                </div>

                                <!-- Date -->
                                <div class="form-field-group">
                                    <label for="date" class="form-label">
                                        <i class="fas fa-calendar text-info"></i>
                                        Date
                                    </label>
                                    <input type="text" id="date" class="enhanced-input">
                                </div>

                                <!-- Language Dropdown -->
                                <div class="form-field-group">
                                    <label for="language" class="form-label">
                                        <i class="fas fa-language text-warning"></i>
                                        Language
                                    </label>
                                    <select id="language" class="enhanced-select language-dropdown">
                                        <option value="">Select language...</option>
                                        <option value="English">English</option>
                                        <option value="Tamil">Tamil</option>
                                        <option value="Not Applicable">Not Applicable</option>
                                        <option value="Kannada">Kannada</option>
                                        <option value="Telugu">Telugu</option>
                                        <option value="Hindi">Hindi</option>
                                        <option value="Malayalam">Malayalam</option>
                                        <option value="Gujarati">Gujarati</option>
                                    </select>
                                </div>

                                <!-- Upload to Google Drive -->
                                <div class="form-field-group">
                                    <label for="uploadToDrive" class="form-label">
                                        <i class="fab fa-google-drive text-success"></i>
                                        Google Drive
                                    </label>
                                    <select id="uploadToDrive" class="enhanced-select">
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                </div>

                                <!-- Access Type -->
                                <div class="form-field-group">
                                    <label for="accessType" class="form-label">
                                        <i class="fas fa-lock text-secondary"></i>
                                        Access Type
                                    </label>
                                    <select id="accessType" class="enhanced-select">
                                        <option value="Public">Public</option>
                                        <option value="Private">Private</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Filename Generation Section -->
                        <div class="mb-4" id="filenameSection">
                            <div class="form-field-group">
                                <h4 class="fw-bold text-gray-900 mb-3 d-flex align-items-center">
                                    <i class="fas fa-file-signature text-secondary me-3"></i>
                                    Auto-Generated Filename
                                </h4>
                                <div class="d-flex gap-3 align-items-end">
                                    <div class="flex-grow-1">
                                        <input type="text" id="generatedFilename" readonly
                                               class="enhanced-input font-monospace fw-bold text-primary"
                                               placeholder="Generate filename from form data...">
                                    </div>
                                    <button type="button" onclick="generateFilename(); return false;"
                                            class="enhanced-btn enhanced-btn-lg btn-success">
                                        <i class="fas fa-magic me-2"></i>Generate
                                    </button>
                                    <button type="button" onclick="confirmFilename(); return false;" id="confirmBtn" disabled
                                            class="enhanced-btn enhanced-btn-lg btn-primary">
                                        <i class="fas fa-lock me-2"></i>Confirm
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer">
                    <div class="d-flex justify-content-end gap-3 w-100">
                        <button type="button" onclick="closeAcceptModal(); return false;"
                                class="enhanced-btn enhanced-btn-lg btn-light text-dark">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" onclick="processIssue(); return false;" id="processBtn" disabled
                                class="enhanced-btn enhanced-btn-lg btn-primary">
                            <i class="fas fa-cogs me-2"></i>Process Issue
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // Global Variables
        let currentIssueId = null;
        let currentIssueData = null;
        let filenameConfirmed = false;
        let allIssues = [];
        let currentBrowserPath = '';
        let selectedFile = null;
        let processingInterval = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Enhanced Dashboard Initializing...');
            loadStats();
            loadIssues();
            setDefaultDate();
            setupSearch();
            setupFileUpload();
            initializeFolderBrowser();
        });

        // CRITICAL: File Upload Setup
        function setupFileUpload() {
            const fileInput = document.getElementById('durationFileInput');
            const dropZone = document.getElementById('fileDropZone');
            const fileInfo = document.getElementById('fileInfo');

            // File input change handler
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleSelectedFile(e.target.files[0]);
                }
            });

            // Drag and drop handlers
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                if (e.dataTransfer.files.length > 0) {
                    handleSelectedFile(e.dataTransfer.files[0]);
                }
            });
        }

        function handleSelectedFile(file) {
            selectedFile = file;

            // Update file info display
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = file.type || 'Unknown';

            // Show file info panel
            document.getElementById('fileInfo').style.display = 'block';

            showToast(`File selected: ${file.name}`, 'info');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // CRITICAL: Upload and Detect Duration Function
        async function uploadAndDetectDuration() {
            if (!selectedFile) {
                showToast('Please select a file first', 'error');
                return;
            }

            const progressContainer = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('uploadProgressBar');

            try {
                // Show progress
                progressContainer.classList.remove('d-none');
                progressBar.style.width = '10%';

                showToast('Uploading file for duration detection...', 'info');

                const formData = new FormData();
                formData.append('file', selectedFile);

                // Simulate progress updates
                const progressInterval = setInterval(() => {
                    const currentWidth = parseInt(progressBar.style.width) || 0;
                    if (currentWidth < 70) {
                        progressBar.style.width = (currentWidth + 10) + '%';
                    }
                }, 200);

                const response = await fetch('/api/detect-duration-file', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });

                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                const result = await response.json();

                if (result.success) {
                    document.getElementById('duration').value = result.duration;
                    showToast(`Duration detected: ${result.duration}`, 'success');

                    // Hide file upload section after successful detection
                    setTimeout(() => {
                        progressContainer.classList.add('d-none');
                        document.getElementById('fileInfo').style.display = 'none';
                        progressBar.style.width = '0%';
                    }, 2000);
                } else {
                    showToast(`Error: ${result.error}`, 'error');
                    progressContainer.classList.add('d-none');
                }

            } catch (error) {
                console.error('Duration detection error:', error);
                showToast('Error detecting duration from file', 'error');
                progressContainer.classList.add('d-none');
            }
        }

        // Detect duration from folder
        async function detectDurationFromFolder() {
            const sourceFolder = document.getElementById('sourceFolder').value;

            if (!sourceFolder) {
                showToast('Please select a source folder first', 'warning');
                return;
            }

            try {
                showToast('Detecting duration from folder...', 'info');

                const response = await fetch('/api/detect-duration', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        source_folder: sourceFolder
                    }),
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('duration').value = result.duration;
                    showToast(`Duration detected: ${result.duration}`, 'success');
                } else {
                    showToast(`Error: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Duration detection error:', error);
                showToast('Error detecting duration from folder', 'error');
            }
        }

        // FAST: Detect duration directly from a server file path (no upload)
        async function detectDurationDirect(absPath) {
            if (!absPath) {
                showToast('No file selected for duration detection', 'warning');
                return;
            }
            try {
                showToast('Detecting duration...', 'info');
                const response = await fetch('/api/detect-duration-direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ file_path: absPath }),
                    credentials: 'include'
                });
                const result = await response.json();
                if (result.success) {
                    document.getElementById('duration').value = result.duration;
                    showToast(`Duration detected: ${result.duration}`, 'success');
                } else {
                    showToast(result.error || 'Could not detect duration', 'error');
                }
            } catch (e) {
                console.error('Direct duration detection error:', e);
                showToast('Error detecting duration', 'error');
            }
        }


        // Initialize folder browser
        function initializeFolderBrowser() {
            loadFolderContents('');
        }

        // Load folder contents
        async function loadFolderContents(path) {
            try {
                const response = await fetch(`/api/browse-folders/${encodeURIComponent(path)}`, {
                    credentials: 'include'
                });

                const data = await response.json();

                if (data.error) {
                    showToast(`Error loading folder: ${data.error}`, 'error');
                    return;
                }

                renderFolderBrowser(data);
                updateBreadcrumb(data.current_path);

            } catch (error) {
                console.error('Error loading folder contents:', error);
                showToast('Error loading folder contents', 'error');
            }
        }

        // Render folder browser
        function renderFolderBrowser(data) {
            const browserElement = document.getElementById('folderBrowser');
            browserElement.innerHTML = '';

            // Add folders
            data.folders.forEach(folder => {
                const folderElement = document.createElement('div');
                folderElement.className = 'folder-item';
                folderElement.innerHTML = `
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas ${folder.icon} text-primary me-3"></i>
                            <div>
                                <div class="fw-semibold">${folder.name}</div>
                                <div class="text-muted small">${folder.size} items</div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="enhanced-btn btn-success btn-sm" data-abs-path="${data.absolute_path}\\\\${folder.name}" data-rel-path="${folder.path}" data-action="select-folder">
                                <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>
                `;

                // Add click handler for navigation
                folderElement.addEventListener('click', (e) => {
                    if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'I') {
                        loadFolderContents(folder.path);
                        currentBrowserPath = folder.path;
                    }
                });

                browserElement.appendChild(folderElement);
            });

            // Add files (for direct duration detection)
            if (data.files && data.files.length > 0) {
                const filesHeader = document.createElement('div');
                filesHeader.className = 'mt-3 mb-2 text-muted fw-semibold';
                filesHeader.textContent = 'Files';
                browserElement.appendChild(filesHeader);

                data.files.forEach(file => {
                    const fileElement = document.createElement('div');
                    fileElement.className = 'folder-item';
                    fileElement.innerHTML = `
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <i class="fas ${file.icon} text-secondary me-3"></i>
                                <div>
                                    <div class="fw-semibold">${file.name}</div>
                                    <div class="text-muted small">${(file.size/ (1024*1024)).toFixed(1)} MB</div>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="enhanced-btn btn-info btn-sm" data-abs-path="${data.absolute_path}\\${file.name}" data-action="detect-duration">
                                    <i class="fas fa-clock me-1"></i>Duration
                                </button>
                            </div>
                        </div>
                    `;
                    browserElement.appendChild(fileElement);
                });
            }

            // Attach button handlers (avoid JS string escape issues)
            browserElement.querySelectorAll('button[data-action="select-folder"]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const absPath = btn.getAttribute('data-abs-path');
                    const relPath = btn.getAttribute('data-rel-path') || '';
                    selectFolder(absPath, relPath);
                });
            });
            browserElement.querySelectorAll('button[data-action="detect-duration"]').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    const absPath = btn.getAttribute('data-abs-path');
                    await detectDurationDirect(absPath);
                });
            });


            // Show message if no folders
            if (data.folders.length === 0) {
                browserElement.innerHTML = '<div class="text-center text-muted p-4">No folders found in this directory</div>';
            }
        }

        // Update breadcrumb
        function updateBreadcrumb(path) {
            const breadcrumbElement = document.getElementById('breadcrumb');
            const pathParts = path ? path.split('/').filter(p => p) : [];

            let breadcrumbHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-server text-primary me-2"></i>
                    <button class="btn btn-link p-0 text-decoration-none" onclick="loadFolderContents('')">Root</button>
            `;

            let currentPath = '';
            pathParts.forEach((part, index) => {
                currentPath += (currentPath ? '/' : '') + part;
                breadcrumbHTML += `
                    <i class="fas fa-chevron-right mx-2 text-muted"></i>
                    <button class="btn btn-link p-0 text-decoration-none" onclick="loadFolderContents('${currentPath}')">${part}</button>
                `;
            });

            breadcrumbHTML += '</div>';
            breadcrumbElement.innerHTML = breadcrumbHTML;
        }

        // Select folder
        function selectFolder(absolutePath, relativePath) {
            document.getElementById('sourceFolder').value = absolutePath;
            document.getElementById('folderPath').textContent = absolutePath;
            document.getElementById('folderInfo').classList.remove('d-none');

            showToast(`Selected folder: ${relativePath || 'Root'}`, 'success');
        }

        // Fetch audio code
        async function fetchAudioCode() {
            if (!currentIssueId) {
                showToast('No issue selected', 'error');
                return;
            }

            try {
                showToast('Fetching audio code...', 'info');

                const response = await fetch(`/api/issues/${currentIssueId}/audio-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.audio_code) {
                    document.getElementById('audioCode').value = result.audio_code;
                    showToast(`Audio code fetched: ${result.audio_code}`, 'success');
                } else {
                    showToast(`Error: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Audio code fetch error:', error);
                showToast('Error fetching audio code', 'error');
            }
        }

        // Generate filename
        async function generateFilename() {
            if (!currentIssueId) {
                showToast('No issue selected', 'error');
                return;
            }

            const formData = {
                ocd_number: document.getElementById('ocdNumber').value,
                audio_code: document.getElementById('audioCode').value,
                type_of_video: document.getElementById('videoType').value,
                title: document.getElementById('title').value,
                date: document.getElementById('date').value,
                language: document.getElementById('language').value,
                duration: document.getElementById('duration').value,
                backup_type: document.getElementById('backupType').value
            };

            // Validate required fields
            if (!formData.ocd_number || !formData.audio_code || !formData.type_of_video || !formData.title) {
                showToast('Please fill in all required fields first', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/issues/${currentIssueId}/generate-filename`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.filename) {
                    document.getElementById('generatedFilename').value = result.filename;
                    document.getElementById('confirmBtn').disabled = false;
                    showToast(`Filename generated successfully`, 'success');
                } else {
                    showToast(`Error: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Filename generation error:', error);
                showToast('Error generating filename', 'error');
            }
        }

        // Confirm filename
        function confirmFilename() {
            const filename = document.getElementById('generatedFilename').value;

            if (filename) {
                filenameConfirmed = true;
                document.getElementById('processBtn').disabled = false;
                document.getElementById('confirmBtn').textContent = '✓ Confirmed';
                document.getElementById('confirmBtn').classList.remove('btn-primary');
                document.getElementById('confirmBtn').classList.add('btn-success');
                showToast('Filename confirmed! Ready to process.', 'success');
            }
        }

        // Enhanced Set default date function 
		function setDefaultDate() {
			const today = new Date()
			const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
			const day = today.getDate().toString().padStart(2, '0')
			const month = monthNames[today.getMonth()]
			const year = today.getFullYear()
			const formattedDate = `${day}-${month}-${year}`
			document.getElementById('date').value = formattedDate
		}


        // Load dashboard statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/dashboard/stats', {
                    credentials: 'include'
                });
                const stats = await response.json();

                document.getElementById('pending-count').textContent = stats.pending || 0;
                document.getElementById('done-today-count').textContent = stats.done_today || 0;
                document.getElementById('rejected-today-count').textContent = stats.rejected_today || 0;
                document.getElementById('done-yesterday-count').textContent = stats.done_yesterday || 0;
                document.getElementById('done-week-count').textContent = stats.done_week || 0;
                document.getElementById('done-month-count').textContent = stats.done_month || 0;

            } catch (error) {
                console.error('Error loading stats:', error);
                showToast('Error loading dashboard statistics', 'error');
            }
        }

        // Load issues from JIRA
        async function loadIssues() {
            const loadingState = document.getElementById('loadingState');
            const tbody = document.getElementById('issues-tbody');

            try {
                loadingState.classList.remove('d-none');
                tbody.innerHTML = '';

                const response = await fetch('/api/jira/issues', {
                    credentials: 'include'
                });

                const issues = await response.json();
                allIssues = issues;

                renderIssues(issues);
                populateAssigneeFilter(issues);

            } catch (error) {
                console.error('Error loading issues:', error);
                showToast('Error loading issues from JIRA', 'error');
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Error loading issues</td></tr>';
            } finally {
                loadingState.classList.add('d-none');
            }
        }

        // Render issues in table
        function renderIssues(issues) {
            const tbody = document.getElementById('issues-tbody');
            tbody.innerHTML = '';

            if (issues.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No issues found</td></tr>';
                return;
            }

            issues.forEach(issue => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="fw-bold text-primary fs-5 mb-1">${issue.id}</div>
                        <div class="text-muted small">${issue.title}</div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="icon-container me-3" style="width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">${issue.project_assignee || 'Unassigned'}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-check-circle me-1"></i>
                            ${issue.status}
                        </span>
                    </td>
                    <td>
                        <div class="fw-bold text-info mb-1">OCD-${issue.ocd_number || 'N/A'}</div>
                        <div class="text-muted small">${issue.backup_type || 'Unknown Type'}</div>
                    </td>
                    <td>
                        <div class="d-flex gap-2">
                            <button class="enhanced-btn btn-success" onclick="acceptIssue('${issue.id}')">
                                <i class="fas fa-check me-2"></i>Accept & Process
                            </button>
                            ${issue.ocd_number && issue.ocd_number !== 'N/A' ?
                                `<button class="enhanced-btn btn-info" onclick="window.open('https://servicedesk.isha.in/browse/OCD-${issue.ocd_number}', '_blank')" title="View OCD Ticket">
                                    <i class="fas fa-eye me-2"></i>View OCD Ticket
                                </button>` :
                                `<button class="enhanced-btn btn-info" onclick="window.open('https://servicedesk.isha.in/browse/${issue.id}', '_blank')" title="View VP Ticket">
                                    <i class="fas fa-eye me-2"></i>View VP Ticket
                                </button>`
                            }
                            <button class="enhanced-btn btn-danger" onclick="rejectIssue('${issue.id}')">
                                <i class="fas fa-times me-2"></i>Reject
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Populate assignee filter
        function populateAssigneeFilter(issues) {
            const filter = document.getElementById('assigneeFilter');
            const assignees = [...new Set(issues.map(issue => issue.project_assignee).filter(Boolean))];

            // Clear existing options except first one
            filter.innerHTML = '<option value="">All Project Assignees</option>';

            assignees.forEach(assignee => {
                const option = document.createElement('option');
                option.value = assignee;
                option.textContent = assignee;
                filter.appendChild(option);
            });
        }

        // Setup search and filter
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const assigneeFilter = document.getElementById('assigneeFilter');

            [searchInput, statusFilter, assigneeFilter].forEach(element => {
                element.addEventListener('input', filterIssues);
            });
        }

        // Filter issues
        function filterIssues() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const assigneeFilter = document.getElementById('assigneeFilter').value;

            const filteredIssues = allIssues.filter(issue => {
                const matchesSearch = issue.id.toLowerCase().includes(searchTerm) ||
                                    issue.title.toLowerCase().includes(searchTerm) ||
                                    (issue.project_assignee || '').toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || issue.status === statusFilter;
                const matchesAssignee = !assigneeFilter || issue.project_assignee === assigneeFilter;

                return matchesSearch && matchesStatus && matchesAssignee;
            });

            renderIssues(filteredIssues);
        }

        // Refresh queue
        function refreshQueue() {
            showToast('Refreshing queue...', 'info');
            loadStats();
            loadIssues();
        }

        // Accept issue
        async function acceptIssue(issueId) {
            currentIssueId = issueId;

            try {
                // Load issue details
                const response = await fetch(`/api/issues/${issueId}/details`, {
                    credentials: 'include'
                });

                const details = await response.json();

                if (details.error) {
                    showToast(`Error: ${details.error}`, 'error');
                    return;
                }

                currentIssueData = details;

                // Populate form with issue details
                document.getElementById('editor').value = details.editor || '';
                document.getElementById('ocdNumber').value = details.ocd_number || '';
                document.getElementById('title').value = details.title || '';
                document.getElementById('backupType').value = details.backup_type || '';

                // Reset form state
                filenameConfirmed = false;
                document.getElementById('processBtn').disabled = true;
                document.getElementById('confirmBtn').disabled = true;
                document.getElementById('confirmBtn').textContent = 'Confirm';
                document.getElementById('confirmBtn').classList.remove('btn-success');
                document.getElementById('confirmBtn').classList.add('btn-primary');

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('acceptModal'));
                modal.show();

                // Load JIRA fields
                loadJiraFields(issueId);

                showToast(`Loading details for ${issueId}`, 'success');

            } catch (error) {
                console.error('Error loading issue details:', error);
                showToast('Error loading issue details', 'error');
            }
        }

        // Load JIRA fields for display
        async function loadJiraFields(issueId) {
            try {
                // Show JIRA fields section
                document.getElementById('jiraFieldsSection').style.display = 'block';

                // Reset to loading state
                const fieldIds = ['jiraVertical', 'jiraVpAudienceType', 'jiraVpTrimbackupFolderName', 'jiraSocialMediaPlatform'];
                fieldIds.forEach(id => {
                    const element = document.getElementById(id);
                    element.innerHTML = '<div class="loading-shimmer rounded" style="height: 1.2rem; width: 80%;"></div>';
                    element.classList.remove('empty');
                });

                // Fetch JIRA fields
                const response = await fetch(`/api/issues/${issueId}/jira-fields`, {
                    credentials: 'include'
                });

                const fields = await response.json();

                if (fields.error) {
                    console.error('Error loading JIRA fields:', fields.error);
                    fieldIds.forEach(id => {
                        const element = document.getElementById(id);
                        element.textContent = 'Error loading';
                        element.classList.add('empty');
                    });
                    return;
                }

                // Populate fields with beautiful display
                const fieldMappings = [
                    { id: 'jiraVertical', value: fields.vertical, fallback: 'Not specified' },
                    { id: 'jiraVpAudienceType', value: fields.vp_audience_type, fallback: 'Not specified' },
                    { id: 'jiraVpTrimbackupFolderName', value: fields.vp_trimbackup_folder_name, fallback: 'Not specified' },
                    { id: 'jiraSocialMediaPlatform', value: fields.social_media_platform, fallback: 'Not specified' }
                ];

                fieldMappings.forEach(field => {
                    const element = document.getElementById(field.id);
                    const value = field.value && field.value.trim() ? field.value : field.fallback;
                    element.textContent = value;

                    if (value === field.fallback) {
                        element.classList.add('empty');
                    } else {
                        element.classList.remove('empty');
                    }
                });

            } catch (error) {
                console.error('Error loading JIRA fields:', error);
                // Hide section on error
                document.getElementById('jiraFieldsSection').style.display = 'none';
            }
        }

        // Reject issue
        function rejectIssue(issueId) {
            currentIssueId = issueId;
            document.getElementById('rejectModal').classList.add('active');
            document.getElementById('rejectReason').value = '';
            document.getElementById('rejectReason').focus();
        }

        // Close reject modal
        function closeRejectModal() {
            document.getElementById('rejectModal').classList.remove('active');
            currentIssueId = null;
        }

        // Confirm reject
        async function confirmReject() {
            const reason = document.getElementById('rejectReason').value.trim();

            if (!reason) {
                showToast('Please provide a reason for rejection', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/issues/${currentIssueId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ reason }),
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.success) {
                    showToast(`Issue ${currentIssueId} rejected successfully`, 'success');
                    closeRejectModal();
                    refreshQueue();
                } else {
                    showToast(`Error: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error rejecting issue:', error);
                showToast('Error rejecting issue', 'error');
            }
        }

        // Close accept modal
        function closeAcceptModal() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('acceptModal'));
            if (modal) {
                modal.hide();
            }

            // Reset processing state
            if (processingInterval) {
                clearInterval(processingInterval);
                processingInterval = null;
            }

            document.getElementById('processingProgressContainer').classList.remove('active');

            // Hide JIRA fields section
            document.getElementById('jiraFieldsSection').style.display = 'none';

            currentIssueId = null;
            currentIssueData = null;
        }

        // CRITICAL: Process Issue with Real-time Progress
        async function processIssue() {
            if (!currentIssueId || !filenameConfirmed) {
                showToast('Please confirm filename before processing', 'warning');
                return;
            }

            const formData = {
                source_folder: document.getElementById('sourceFolder').value,
                editor: document.getElementById('editor').value,
                ocd_number: document.getElementById('ocdNumber').value,
                audio_code: document.getElementById('audioCode').value,
                title: document.getElementById('title').value,
                duration: document.getElementById('duration').value,
                backup_type: document.getElementById('backupType').value,
                type_of_video: document.getElementById('videoType').value,
                date: document.getElementById('date').value,
                language: document.getElementById('language').value,
                upload_to_drive: document.getElementById('uploadToDrive').value,
                access_type: document.getElementById('accessType').value,
                filename: document.getElementById('generatedFilename').value,
                project_assignee: currentIssueData?.project_assignee || ''
            };

            // Validate required fields
            const requiredFields = ['source_folder', 'ocd_number', 'audio_code', 'title', 'duration', 'type_of_video'];
            const missingFields = requiredFields.filter(field => !formData[field]);

            if (missingFields.length > 0) {
                showToast(`Missing required fields: ${missingFields.join(', ')}`, 'warning');
                return;
            }

            try {
                // Hide form sections and show progress
                document.getElementById('folderBrowserSection').style.display = 'none';
                document.getElementById('autoFieldsSection').style.display = 'none';
                document.getElementById('configSection').style.display = 'none';
                document.getElementById('filenameSection').style.display = 'none';
                document.getElementById('processingProgressContainer').classList.add('active');
                document.getElementById('processBtn').style.display = 'none';

                showToast(`Starting processing for ${currentIssueId}...`, 'info');

                // Start processing
                const response = await fetch(`/api/issues/${currentIssueId}/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.success) {
                    // Start polling for progress updates
                    startProgressPolling();
                } else {
                    showToast(`Processing failed: ${result.error}`, 'error');
                    resetProcessingUI();
                }

            } catch (error) {
                console.error('Processing error:', error);
                showToast('Error starting processing', 'error');
                resetProcessingUI();
            }
        }

        // 🚀 SPECTACULAR PROGRESS POLLING WITH REAL-TIME UPDATES
        let processingStartTime = null;
        let consoleAutoScroll = true;

        function startProgressPolling() {
            processingStartTime = Date.now();

            // Initialize spectacular progress UI
            initializeSpectacularProgress();

            processingInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/issues/${currentIssueId}/processing-status`, {
                        credentials: 'include'
                    });

                    const status = await response.json();
                    updateSpectacularProgress(status);

                    if (status.status === 'complete' || status.status === 'error') {
                        clearInterval(processingInterval);
                        processingInterval = null;

                        if (status.status === 'complete') {
                            completeSpectacularProgress();
                            setTimeout(() => {
                                closeAcceptModal();
                                refreshQueue();
                            }, 5000);
                        } else {
                            errorSpectacularProgress(status.error);
                            setTimeout(() => {
                                resetProcessingUI();
                            }, 3000);
                        }
                    }

                } catch (error) {
                    console.error('Progress polling error:', error);
                    addConsoleMessage('ERROR', `Progress polling failed: ${error.message}`, 'error');
                }
            }, 800); // Faster polling for smoother updates
        }

        // 🌟 SPECTACULAR PROGRESS INITIALIZATION
        function initializeSpectacularProgress() {
            // Hide JIRA fields section during processing
            document.getElementById('jiraFieldsSection').style.display = 'none';

            // Reset all progress elements
            document.getElementById('processingProgressBar').style.width = '0%';
            document.getElementById('processingPercentage').textContent = '0';
            document.getElementById('processingStatusText').textContent = 'Initializing comprehensive video processing pipeline...';
            document.getElementById('currentStepCounter').textContent = '0/14';
            document.getElementById('processingSpeed').textContent = 'Initializing...';
            document.getElementById('processingTime').textContent = '00:00';

            // Reset all step items
            document.querySelectorAll('.step-item').forEach(item => {
                item.classList.remove('active', 'completed');
            });

            // Clear console and add startup messages
            clearConsole();
            addConsoleMessage('SYSTEM', '🎬 Archives TrimsFlow processing system initialized...', 'startup');
            addConsoleMessage('INFO', `Starting comprehensive processing for VP ${currentIssueId}`, 'info');
            addConsoleMessage('INFO', 'Ready to begin 14-step video processing pipeline', 'info');

            // Start processing timer
            updateProcessingTimer();
        }

        // 🎯 SPECTACULAR PROGRESS UPDATE WITH CONSOLE INTEGRATION
        function updateSpectacularProgress(status) {
            const progressBar = document.getElementById('processingProgressBar');
            const percentage = document.getElementById('processingPercentage');
            const statusText = document.getElementById('processingStatusText');
            const currentStepText = document.getElementById('currentStepText');
            const currentStepCounter = document.getElementById('currentStepCounter');
            const currentStepIcon = document.getElementById('currentStepIcon');

            // Calculate smooth progress
            const targetProgress = status.progress || 0;
            const currentProgress = parseInt(progressBar.style.width) || 0;
            const currentStep = status.current_step || 0;
            const stepName = status.step_name || 'Processing...';

            // Animate progress bar with spectacular effects
            if (targetProgress > currentProgress) {
                animateSpectacularProgressBar(progressBar, currentProgress, targetProgress);
            }

            // Animate percentage counter with smooth transitions
            animateSpectacularCounter(percentage, currentProgress, targetProgress);

            // Update status text with fade and glow effects
            if (statusText.textContent !== stepName) {
                statusText.style.opacity = '0.5';
                statusText.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    statusText.textContent = stepName;
                    statusText.style.opacity = '1';
                    statusText.style.transform = 'translateY(0)';
                }, 200);
            }

            // Update current step indicator with spectacular effects
            if (currentStepText.textContent !== stepName) {
                currentStepText.style.opacity = '0.5';
                setTimeout(() => {
                    currentStepText.textContent = stepName;
                    currentStepText.style.opacity = '1';

                    // Update step icon based on current step
                    updateStepIcon(currentStepIcon, currentStep);
                }, 150);
            }

            // Update step counter
            currentStepCounter.textContent = `${currentStep}/14`;

            // Update step indicators with spectacular animations
            updateSpectacularStepIndicators(currentStep);

            // Add console message for step progress
            if (status.step_name && status.step_name !== 'Processing...') {
                addConsoleMessage('STEP', `Step ${currentStep}/14: ${status.step_name}`, 'info');
            }

            // Update processing speed and time
            updateProcessingStats(targetProgress, currentStep);

            console.log(`🎬 Progress: ${targetProgress}% - Step ${currentStep}/14 - ${stepName}`);
        }

        // 🎨 SPECTACULAR PROGRESS BAR ANIMATION
        function animateSpectacularProgressBar(progressBar, from, to) {
            const duration = 1200;
            const startTime = performance.now();

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Advanced easing function for spectacular animation
                const easeOutElastic = progress === 0 ? 0 : progress === 1 ? 1 :
                    Math.pow(2, -10 * progress) * Math.sin((progress * 10 - 0.75) * (2 * Math.PI) / 3) + 1;

                const currentValue = from + (to - from) * easeOutElastic;
                progressBar.style.width = `${Math.min(currentValue, to)}%`;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }

            requestAnimationFrame(animate);
        }

        // 🔢 SPECTACULAR COUNTER ANIMATION
        function animateSpectacularCounter(element, from, to) {
            const duration = 800;
            const startTime = performance.now();

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Smooth easing with bounce effect
                const easeOutBounce = progress < 1/2.75 ? 7.5625 * progress * progress :
                    progress < 2/2.75 ? 7.5625 * (progress -= 1.5/2.75) * progress + 0.75 :
                    progress < 2.5/2.75 ? 7.5625 * (progress -= 2.25/2.75) * progress + 0.9375 :
                    7.5625 * (progress -= 2.625/2.75) * progress + 0.984375;

                const currentValue = Math.round(from + (to - from) * easeOutBounce);
                element.textContent = currentValue;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }

            requestAnimationFrame(animate);
        }

        // 💻 SPECTACULAR CONSOLE FUNCTIONS
        function addConsoleMessage(type, message, level = 'info') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();

            const consoleLine = document.createElement('div');
            consoleLine.className = `console-line ${level}`;
            consoleLine.innerHTML = `
                <span class="console-timestamp">[${type}]</span>
                <span class="console-message">${message}</span>
            `;

            consoleOutput.appendChild(consoleLine);

            // Auto-scroll to bottom if enabled
            if (consoleAutoScroll) {
                scrollToBottomConsole();
            }

            // Add entrance animation
            consoleLine.style.opacity = '0';
            consoleLine.style.transform = 'translateX(-20px)';
            setTimeout(() => {
                consoleLine.style.opacity = '1';
                consoleLine.style.transform = 'translateX(0)';
                consoleLine.style.transition = 'all 0.3s ease';
            }, 50);
        }

        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }

        function scrollToBottomConsole() {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function toggleConsoleAutoScroll() {
            consoleAutoScroll = !consoleAutoScroll;
            const btn = document.getElementById('autoScrollBtn');
            btn.style.color = consoleAutoScroll ? '#10b981' : '#6b7280';
            btn.title = consoleAutoScroll ? 'Auto-scroll: ON' : 'Auto-scroll: OFF';
        }

        // ⏱️ PROCESSING TIMER AND STATS
        let processingTimerInterval = null;

        function updateProcessingTimer() {
            if (processingTimerInterval) {
                clearInterval(processingTimerInterval);
            }

            processingTimerInterval = setInterval(() => {
                if (processingStartTime) {
                    const elapsed = Date.now() - processingStartTime;
                    const minutes = Math.floor(elapsed / 60000);
                    const seconds = Math.floor((elapsed % 60000) / 1000);
                    document.getElementById('processingTime').textContent =
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        function updateProcessingStats(progress, currentStep) {
            const speedElement = document.getElementById('processingSpeed');

            if (processingStartTime && progress > 0) {
                const elapsed = (Date.now() - processingStartTime) / 1000;
                const stepsPerSecond = currentStep / elapsed;
                const estimatedTotal = 14 / stepsPerSecond;
                const remaining = estimatedTotal - elapsed;

                if (remaining > 0) {
                    const remainingMinutes = Math.floor(remaining / 60);
                    const remainingSeconds = Math.floor(remaining % 60);
                    speedElement.textContent = `~${remainingMinutes}:${remainingSeconds.toString().padStart(2, '0')} remaining`;
                } else {
                    speedElement.textContent = 'Finalizing...';
                }
            }
        }

        // 🎉 COMPLETION HANDLERS
        function completeSpectacularProgress() {
            // Final progress update
            document.getElementById('processingProgressBar').style.width = '100%';
            document.getElementById('processingPercentage').textContent = '100';
            document.getElementById('processingStatusText').textContent = '🎉 Processing Complete!';
            document.getElementById('currentStepText').textContent = '🎉 All steps completed successfully!';
            document.getElementById('currentStepCounter').textContent = '14/14';
            document.getElementById('processingSpeed').textContent = 'Completed!';

            // Mark all steps as completed
            document.querySelectorAll('.step-item').forEach(item => {
                item.classList.remove('active');
                item.classList.add('completed');
            });

            // Add completion messages to console
            addConsoleMessage('SUCCESS', '🎉 All processing steps completed successfully!', 'success');
            addConsoleMessage('INFO', 'Video processing pipeline finished', 'info');
            addConsoleMessage('SYSTEM', 'Preparing to close modal and refresh queue...', 'startup');

            // Show success toast
            showToast('🎉 Processing completed successfully! Closing in 5 seconds...', 'success');

            // Clear timer
            if (processingTimerInterval) {
                clearInterval(processingTimerInterval);
            }
        }

        function errorSpectacularProgress(error) {
            // Update UI for error state
            document.getElementById('processingStatusText').textContent = '❌ Processing Failed';
            document.getElementById('currentStepText').textContent = `❌ Error: ${error}`;
            document.getElementById('processingSpeed').textContent = 'Failed';

            // Add error messages to console
            addConsoleMessage('ERROR', `❌ Processing failed: ${error}`, 'error');
            addConsoleMessage('SYSTEM', 'Resetting UI in 3 seconds...', 'startup');

            // Show error toast
            showToast(`❌ Processing failed: ${error}`, 'error');

            // Clear timer
            if (processingTimerInterval) {
                clearInterval(processingTimerInterval);
            }
        }

        // 🎯 UPDATE STEP ICON BASED ON CURRENT STEP
        function updateStepIcon(iconElement, currentStep) {
            const stepIcons = {
                1: 'fa-table', 2: 'fa-copy', 3: 'fa-edit', 4: 'fa-folder-open',
                5: 'fa-file-video', 6: 'fa-music', 7: 'fa-tags', 8: 'fa-cogs',
                9: 'fa-closed-captioning', 10: 'fa-check-circle', 11: 'fa-volume-up',
                12: 'fa-cloud-upload-alt', 13: 'fa-link', 14: 'fa-flag-checkered'
            };

            // Remove all existing icon classes
            iconElement.className = iconElement.className.replace(/fa-[\w-]+/g, '');

            // Add new icon class
            const newIcon = stepIcons[currentStep] || 'fa-play-circle';
            iconElement.classList.add('fas', newIcon, 'step-icon');
        }

        // 🌟 UPDATE SPECTACULAR STEP INDICATORS
        function updateSpectacularStepIndicators(currentStep) {
            const stepItems = document.querySelectorAll('.step-item');

            stepItems.forEach((item, index) => {
                const stepNumber = index + 1;
                const circle = item.querySelector('.step-circle');
                const progressBar = item.querySelector('.step-progress-bar');

                // Remove all states
                item.classList.remove('active', 'completed');

                if (stepNumber < currentStep) {
                    // Completed step with spectacular animation
                    item.classList.add('completed');

                    if (circle && !circle.dataset.completed) {
                        circle.dataset.completed = 'true';

                        // Trigger completion animation
                        setTimeout(() => {
                            circle.style.transform = 'scale(1.2)';
                            setTimeout(() => {
                                circle.style.transform = 'scale(1.05)';
                            }, 200);
                        }, 100);
                    }

                    if (progressBar) {
                        progressBar.style.width = '100%';
                    }

                } else if (stepNumber === currentStep) {
                    // Active step with pulsing animation
                    item.classList.add('active');

                    if (circle) {
                        circle.dataset.completed = 'false';
                        circle.style.transform = 'scale(1.05)';
                    }

                    if (progressBar) {
                        progressBar.style.width = '100%';
                    }

                } else {
                    // Future step - reset state
                    if (circle) {
                        circle.dataset.completed = 'false';
                        circle.style.transform = 'scale(1)';
                    }

                    if (progressBar) {
                        progressBar.style.width = '0%';
                    }
                }
            });
        }

        // Reset processing UI
        function resetProcessingUI() {
            document.getElementById('folderBrowserSection').style.display = 'block';
            document.getElementById('autoFieldsSection').style.display = 'block';
            document.getElementById('configSection').style.display = 'block';
            document.getElementById('filenameSection').style.display = 'block';
            document.getElementById('processingProgressContainer').classList.remove('active');
            document.getElementById('processBtn').style.display = 'block';

            // Clear timers
            if (processingTimerInterval) {
                clearInterval(processingTimerInterval);
                processingTimerInterval = null;
            }

            processingStartTime = null;
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast-notification ${type}`;

            const icon = {
                success: 'fa-check-circle',
                error: 'fa-times-circle',
                warning: 'fa-exclamation-triangle',
                info: 'fa-info-circle'
            }[type] || 'fa-info-circle';

            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas ${icon} me-3 fs-5"></i>
                    <div>
                        <div class="fw-semibold">${message}</div>
                        <div class="text-muted small">${new Date().toLocaleTimeString()}</div>
                    </div>
                    <button class="btn btn-sm btn-link ms-auto" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            container.appendChild(toast);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

                // SPECTACULAR Live Console Functions
        function addConsoleOutput(message, progress, type = 'info') {
            const consoleOutput = document.getElementById('consoleOutput');
            if (!consoleOutput) return; // Safety check
            
            const timestamp = new Date().toLocaleTimeString();
            
            const consoleLine = document.createElement('div');
            consoleLine.className = `console-line ${type}`;
            
            consoleLine.innerHTML = `
                <span class="console-timestamp">[${timestamp}]</span>
                <span class="console-message">${message}${progress ? ` (${progress}%)` : ''}</span>
            `;
            
            consoleOutput.appendChild(consoleLine);
            
            // Auto-scroll to bottom
            scrollToBottomConsole();
            
            // Limit console lines (keep last 50)
            const lines = consoleOutput.querySelectorAll('.console-line');
            if (lines.length > 50) {
                lines[0].remove();
            }
        }

        function clearConsole() {
            const consoleOutput = document.getElementById('consoleOutput');
            if (!consoleOutput) return; // Safety check
            
            consoleOutput.innerHTML = `
                <div class="console-line startup">
                    <span class="console-timestamp">[SYSTEM]</span>
                    <span class="console-message">Console cleared by user</span>
                </div>
            `;
        }

        function scrollToBottomConsole() {
            const consoleOutput = document.getElementById('consoleOutput');
            if (!consoleOutput) return; // Safety check
            
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // Enhanced Step Indicators Update
        function updateStepIndicators(currentStep) {
            const stepItems = document.querySelectorAll('.step-item');
            
            stepItems.forEach((item, index) => {
                const stepNumber = index + 1;
                const circle = item.querySelector('.step-circle');
                
                // Remove all classes
                item.classList.remove('active', 'completed');
                
                if (stepNumber < currentStep) {
                    // Completed steps
                    item.classList.add('completed');
                    if (!circle.classList.contains('completed-animated')) {
                        circle.classList.add('completed-animated');
                        setTimeout(() => circle.classList.remove('completed-animated'), 600);
                    }
                } else if (stepNumber === currentStep) {
                    // Active step
                    item.classList.add('active');
                }
                // Future steps remain default
            });
        }

        function animateProgressBar(progressBar, targetProgress) {
            if (!progressBar) return; // Safety check
            
            const currentProgress = parseInt(progressBar.style.width) || 0;
            const duration = 1000;
            const startTime = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Use cubic-bezier easing for smooth animation
                const easeProgress = 1 - Math.pow(1 - progress, 3);
                const currentValue = currentProgress + (targetProgress - currentProgress) * easeProgress;
                
                progressBar.style.width = `${currentValue}%`;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }
            
            requestAnimationFrame(animate);
        }

        function animateCounter(element, targetValue) {
            if (!element) return; // Safety check
            
            const currentValue = parseInt(element.textContent) || 0;
            const duration = 800;
            const startTime = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentCount = Math.round(currentValue + (targetValue - currentValue) * progress);
                element.textContent = currentCount;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }
            
            requestAnimationFrame(animate);
        }

        // Enhanced updateProcessingProgress with Console Integration
        function updateProcessingProgress(status) {
            const progressBar = document.getElementById('processingProgressBar');
            const percentage = document.getElementById('processingPercentage');
            const statusText = document.getElementById('processingStatusText');
            const currentStepText = document.getElementById('currentStepText');
            
            // Smooth progress bar animation
            const targetProgress = status.progress || 0;
            animateProgressBar(progressBar, targetProgress);
            
            // Animate percentage counter
            animateCounter(percentage, targetProgress);
            
            // Update status text with fade effect
            if (statusText && statusText.textContent !== status.stepname) {
                statusText.style.opacity = '0.5';
                setTimeout(() => {
                    statusText.textContent = status.stepname || 'Processing...';
                    statusText.style.opacity = '1';
                }, 150);
            }
            
            // Update current step indicator
            if (currentStepText) {
                currentStepText.textContent = `Step ${status.currentstep || 1}/15: ${status.stepname || 'Processing...'}`;
            }
            
            // Update step indicators with spectacular animations
            updateStepIndicators(status.currentstep || 1);
            
            // Add console output
            addConsoleOutput(status.stepname, status.progress, 'info');
            
            console.log(`Progress: ${targetProgress}% - Step ${status.currentstep}/15 - ${status.stepname}`);
        }

        // Initialize tooltips and other bootstrap components + Enhanced Dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize enhanced dashboard functionality
            console.log('Enhanced Dashboard Initializing...');
            if (typeof loadStats === 'function') loadStats();
            if (typeof loadIssues === 'function') loadIssues();
            if (typeof setDefaultDate === 'function') setDefaultDate();
            if (typeof setupSearch === 'function') setupSearch();
            if (typeof setupFileUpload === 'function') setupFileUpload();
            if (typeof initializeFolderBrowser === 'function') initializeFolderBrowser();

            console.log('Archives TrimsFlow Dashboard Ready! 🚀 with SPECTACULAR Processing UI');
        });
    </script>
</body>
</html>

