<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archives TrimsFlow - Ultimate Video Processing Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
            animation: pulse-glow 2s infinite;
        }
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
            50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- Floating Background Elements -->
    <div class="absolute top-20 left-20 w-32 h-32 bg-white opacity-10 rounded-full floating"></div>
    <div class="absolute top-40 right-32 w-24 h-24 bg-white opacity-10 rounded-full floating" style="animation-delay: -2s;"></div>
    <div class="absolute bottom-32 left-1/3 w-20 h-20 bg-white opacity-10 rounded-full floating" style="animation-delay: -4s;"></div>
    <div class="absolute bottom-20 right-20 w-28 h-28 bg-white opacity-10 rounded-full floating" style="animation-delay: -3s;"></div>

    <div class="max-w-md w-full mx-4 space-y-8">
        <div class="glass-effect rounded-3xl shadow-2xl p-10 pulse-glow">
            <!-- Header with Logo -->
            <div class="text-center mb-10">
                <div class="mx-auto h-20 w-20 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mb-6 shadow-lg">
                    <i class="fas fa-video text-white text-3xl"></i>
                </div>
                <h1 class="text-4xl font-bold text-white mb-2">Archives TrimsFlow</h1>
                <p class="text-blue-100 text-lg font-medium">Ultimate Video Processing Platform</p>
                <div class="h-1 w-20 bg-gradient-to-r from-blue-400 to-purple-500 mx-auto mt-4 rounded-full"></div>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="bg-red-500 bg-opacity-20 border border-red-400 text-red-100 px-4 py-3 rounded-lg mb-6 backdrop-blur-sm">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {{ message }}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Login Form -->
            <form method="POST" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-semibold text-blue-100 mb-3">Username</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center">
                            <i class="fas fa-user text-blue-300"></i>
                        </div>
                        <input id="username" name="username" type="text" required
                               class="pl-12 w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent text-white placeholder-blue-200 backdrop-blur-sm transition duration-300"
                               placeholder="Enter your username" value="admin">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-semibold text-blue-100 mb-3">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center">
                            <i class="fas fa-lock text-blue-300"></i>
                        </div>
                        <input id="password" name="password" type="password" required
                               class="pl-12 w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent text-white placeholder-blue-200 backdrop-blur-sm transition duration-300"
                               placeholder="Enter your password">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember_me" name="remember_me" type="checkbox" 
                               class="h-4 w-4 text-blue-400 focus:ring-blue-400 border-white border-opacity-30 rounded bg-white bg-opacity-20">
                        <label for="remember_me" class="ml-3 block text-sm text-blue-100">Remember me</label>
                    </div>
                </div>

                <button type="submit" 
                        class="w-full flex justify-center py-4 px-6 border border-transparent rounded-xl shadow-lg text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-400 focus:ring-opacity-50 transform transition hover:scale-105 duration-300">
                    <i class="fas fa-sign-in-alt mr-3"></i>
                    Sign Into Platform
                </button>
            </form>

            <!-- Features Showcase -->
            <div class="mt-8 grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="w-10 h-10 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-tasks text-green-300"></i>
                    </div>
                    <p class="text-xs text-blue-100">66 Tickets</p>
                </div>
                <div class="text-center">
                    <div class="w-10 h-10 bg-yellow-500 bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-users text-yellow-300"></i>
                    </div>
                    <p class="text-xs text-blue-100">Multi-User</p>
                </div>
                <div class="text-center">
                    <div class="w-10 h-10 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-folder-open text-purple-300"></i>
                    </div>
                    <p class="text-xs text-blue-100">Smart Browse</p>
                </div>
                <div class="text-center">
                    <div class="w-10 h-10 bg-pink-500 bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-magic text-pink-300"></i>
                    </div>
                    <p class="text-xs text-blue-100">Auto-Fill</p>
                </div>
            </div>

            <!-- Demo Credentials -->
            <div class="mt-8 p-4 bg-gradient-to-r from-green-500 to-blue-600 bg-opacity-20 rounded-xl backdrop-blur-sm">
                <p class="text-xs text-center text-white">
                    <i class="fas fa-key mr-2"></i>
                    <strong>Demo Access:</strong> <code class="bg-white bg-opacity-20 px-2 py-1 rounded">admin</code> / <code class="bg-white bg-opacity-20 px-2 py-1 rounded">admin123</code>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center">
            <p class="text-blue-100 text-sm">
                © 2025 Archives TrimsFlow - Professional Video Workflow Platform
            </p>
        </div>
    </div>
</body>
</html>
